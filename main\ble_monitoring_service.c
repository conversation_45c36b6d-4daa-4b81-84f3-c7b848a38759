/**
 * @file ble_monitoring_service.c
 * @brief BLE Monitoring Service Implementation
 * @version 1.0
 * @date 2024-08-16
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "host/ble_gatt.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include "ble_monitoring_service.h"
#include "ble_config_service.h"
#include "temp_sensor.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "BLE_MONITOR_SERVICE";

// Monitoring Service UUID: FFF0 (changed from FFE0 to avoid conflict with config service)
static const ble_uuid16_t gatt_monitor_svc_uuid = BLE_UUID16_INIT(0xFFF0);

// Characteristic UUIDs (changed to avoid conflict with config service)
static const ble_uuid16_t gatt_vitals_chr_uuid = BLE_UUID16_INIT(0xFFF1);
static const ble_uuid16_t gatt_fall_chr_uuid = BLE_UUID16_INIT(0xFFF2);
static const ble_uuid16_t gatt_presence_chr_uuid = BLE_UUID16_INIT(0xFFF3);
static const ble_uuid16_t gatt_sensors_chr_uuid = BLE_UUID16_INIT(0xFFF4);

// Characteristic handles
static uint16_t vitals_char_handle;
static uint16_t fall_char_handle;
static uint16_t presence_char_handle;
static uint16_t sensors_char_handle;

// Data buffers
static uint8_t vitals_data_buffer[4] = {0}; // 2 bytes heart rate + 2 bytes respiratory rate
static uint8_t fall_data_buffer[1] = {0};   // 1 byte status
static uint8_t presence_data_buffer[3] = {0}; // 1 byte status + 2 bytes distance
static uint8_t sensors_data_buffer[5] = {0}; // 2 bytes light + 1 byte PIR + 2 bytes temperature

// Forward declaration of access callback
static int ble_monitoring_access(uint16_t conn_handle, uint16_t attr_handle,
                               struct ble_gatt_access_ctxt *ctxt, void *arg);

// GATT Service definition
static const struct ble_gatt_svc_def gatt_monitoring_svcs[] = {
    {
        // Service
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = &gatt_monitor_svc_uuid.u,
        .characteristics = (struct ble_gatt_chr_def[]) {
            {
                // Vitals Monitoring Characteristic
                .uuid = &gatt_vitals_chr_uuid.u,
                .access_cb = ble_monitoring_access,
                .flags = BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &vitals_char_handle,
            },
            {
                // Fall Detection Characteristic
                .uuid = &gatt_fall_chr_uuid.u,
                .access_cb = ble_monitoring_access,
                .flags = BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &fall_char_handle,
            },
            {
                // Presence Detection Characteristic
                .uuid = &gatt_presence_chr_uuid.u,
                .access_cb = ble_monitoring_access,
                .flags = BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &presence_char_handle,
            },
            {
                // Additional Sensors Characteristic
                .uuid = &gatt_sensors_chr_uuid.u,
                .access_cb = ble_monitoring_access,
                .flags = BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &sensors_char_handle,
            },
            {
                0, // No more characteristics in this service
            }
        },
    },
    {
        0, // No more services
    },
};

// Access callback for the monitoring service
static int ble_monitoring_access(uint16_t conn_handle, uint16_t attr_handle,
                               struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    int rc;

    ESP_LOGI(TAG, "监测服务访问回调被调用，操作: %d, 句柄: %d", ctxt->op, attr_handle);

    switch (ctxt->op) {
        case BLE_GATT_ACCESS_OP_READ_CHR:
            if (attr_handle == vitals_char_handle) {
                ESP_LOGI(TAG, "Read vitals data");
                rc = os_mbuf_append(ctxt->om, vitals_data_buffer, sizeof(vitals_data_buffer));
                return rc == 0 ? 0 : BLE_ATT_ERR_INSUFFICIENT_RES;
            } else if (attr_handle == fall_char_handle) {
                ESP_LOGI(TAG, "Read fall data");
                rc = os_mbuf_append(ctxt->om, fall_data_buffer, sizeof(fall_data_buffer));
                return rc == 0 ? 0 : BLE_ATT_ERR_INSUFFICIENT_RES;
            } else if (attr_handle == presence_char_handle) {
                ESP_LOGI(TAG, "Read presence data");
                rc = os_mbuf_append(ctxt->om, presence_data_buffer, sizeof(presence_data_buffer));
                return rc == 0 ? 0 : BLE_ATT_ERR_INSUFFICIENT_RES;
            } else if (attr_handle == sensors_char_handle) {
                ESP_LOGI(TAG, "Read sensors data");
                rc = os_mbuf_append(ctxt->om, sensors_data_buffer, sizeof(sensors_data_buffer));
                return rc == 0 ? 0 : BLE_ATT_ERR_INSUFFICIENT_RES;
            }
            break;

        default:
            break;
    }

    // Unknown characteristic
    return BLE_ATT_ERR_UNLIKELY;
}

// Initialize the monitoring service
esp_err_t ble_monitoring_service_init(void)
{
    int rc;

    // 打印服务和特征值 UUID 以便调试
    char buf[BLE_UUID_STR_LEN];
    ESP_LOGI(TAG, "监测服务 UUID: %s", ble_uuid_to_str(&gatt_monitor_svc_uuid.u, buf));
    ESP_LOGI(TAG, "生命体征特征值 UUID: %s", ble_uuid_to_str(&gatt_vitals_chr_uuid.u, buf));
    ESP_LOGI(TAG, "跌倒检测特征值 UUID: %s", ble_uuid_to_str(&gatt_fall_chr_uuid.u, buf));
    ESP_LOGI(TAG, "人体存在特征值 UUID: %s", ble_uuid_to_str(&gatt_presence_chr_uuid.u, buf));
    ESP_LOGI(TAG, "附加传感器特征值 UUID: %s", ble_uuid_to_str(&gatt_sensors_chr_uuid.u, buf));

    // 初始化特征值句柄
    vitals_char_handle = 0;
    fall_char_handle = 0;
    presence_char_handle = 0;
    sensors_char_handle = 0;

    // Add the service to the BLE stack
    rc = ble_gatts_count_cfg(gatt_monitoring_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to count GATT service config: %d", rc);
        return ESP_FAIL;
    }

    rc = ble_gatts_add_svcs(gatt_monitoring_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to add GATT service: %d", rc);
        return ESP_FAIL;
    }

    // 检查特征值句柄是否已分配
    ESP_LOGI(TAG, "监测服务初始化成功，特征值句柄: vitals=%d, fall=%d, presence=%d, sensors=%d",
             vitals_char_handle, fall_char_handle, presence_char_handle, sensors_char_handle);

    return ESP_OK;
}

// Callback for GATT service registration
void ble_monitoring_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg)
{
    char buf[BLE_UUID_STR_LEN];

    switch (ctxt->op) {
        case BLE_GATT_REGISTER_OP_SVC:
            ESP_LOGI(TAG, "Registered service %s with handle=%d",
                     ble_uuid_to_str(ctxt->svc.svc_def->uuid, buf), ctxt->svc.handle);
            break;

        case BLE_GATT_REGISTER_OP_CHR:
            ESP_LOGI(TAG, "Registered characteristic %s with def_handle=%d val_handle=%d",
                     ble_uuid_to_str(ctxt->chr.chr_def->uuid, buf),
                     ctxt->chr.def_handle, ctxt->chr.val_handle);

            // 检查是哪个特征值被注册了
            if (ble_uuid_cmp(ctxt->chr.chr_def->uuid, &gatt_vitals_chr_uuid.u) == 0) {
                ESP_LOGI(TAG, "Vitals characteristic registered with handle=%d", ctxt->chr.val_handle);
                vitals_char_handle = ctxt->chr.val_handle;
            } else if (ble_uuid_cmp(ctxt->chr.chr_def->uuid, &gatt_fall_chr_uuid.u) == 0) {
                ESP_LOGI(TAG, "Fall detection characteristic registered with handle=%d", ctxt->chr.val_handle);
                fall_char_handle = ctxt->chr.val_handle;
            } else if (ble_uuid_cmp(ctxt->chr.chr_def->uuid, &gatt_presence_chr_uuid.u) == 0) {
                ESP_LOGI(TAG, "Presence detection characteristic registered with handle=%d", ctxt->chr.val_handle);
                presence_char_handle = ctxt->chr.val_handle;
            } else if (ble_uuid_cmp(ctxt->chr.chr_def->uuid, &gatt_sensors_chr_uuid.u) == 0) {
                ESP_LOGI(TAG, "Additional sensors characteristic registered with handle=%d", ctxt->chr.val_handle);
                sensors_char_handle = ctxt->chr.val_handle;
            }
            break;

        default:
            break;
    }
}

// Update vitals monitoring data
esp_err_t ble_monitoring_update_vitals(vitals_data_t *data)
{
    // Update data buffer
    memcpy(vitals_data_buffer, data, sizeof(vitals_data_buffer));

    // Send notification
    ble_gatts_chr_updated(vitals_char_handle);
    ESP_LOGI(TAG, "Vitals data updated: HR=%d, RR=%d", data->heart_rate, data->respiratory_rate);

    return ESP_OK;
}

// Update fall detection data
esp_err_t ble_monitoring_update_fall(fall_data_t *data)
{
    // Update data buffer
    memcpy(fall_data_buffer, data, sizeof(fall_data_buffer));

    // Send notification
    ble_gatts_chr_updated(fall_char_handle);
    ESP_LOGI(TAG, "Fall data updated: status=%d", data->status);

    return ESP_OK;
}

// Update presence detection data
esp_err_t ble_monitoring_update_presence(presence_data_t *data)
{
    // Update data buffer
    memcpy(presence_data_buffer, data, sizeof(presence_data_buffer));

    // Send notification
    ble_gatts_chr_updated(presence_char_handle);
    ESP_LOGI(TAG, "Presence data updated: status=%d, distance=%d cm",
             data->status, data->distance);

    return ESP_OK;
}

// Update additional sensors data
esp_err_t ble_monitoring_update_sensors(additional_sensors_data_t *data)
{
    // 手动填充数据缓冲区，确保正确的内存布局
    // 字节0-1: 光线传感器值 (uint16)
    sensors_data_buffer[0] = data->light & 0xFF;
    sensors_data_buffer[1] = (data->light >> 8) & 0xFF;

    // 字节2: PIR传感器状态 (uint8)
    sensors_data_buffer[2] = data->pir;

    // 字节3-4: 芯片温度 (int16)
    sensors_data_buffer[3] = data->temperature & 0xFF;
    sensors_data_buffer[4] = (data->temperature >> 8) & 0xFF;

    // 打印发送的原始数据以便调试
    ESP_LOGI(TAG, "Raw sensor data buffer: [0x%02x, 0x%02x, 0x%02x, 0x%02x, 0x%02x]",
             sensors_data_buffer[0], sensors_data_buffer[1], sensors_data_buffer[2],
             sensors_data_buffer[3], sensors_data_buffer[4]);

    // Send notification
    ble_gatts_chr_updated(sensors_char_handle);
    ESP_LOGI(TAG, "Sensors data updated: light=%d lux, PIR=%d, temp=%.1f°C",
             data->light, data->pir, data->temperature / 10.0f);

    return ESP_OK;
}

// Send simulated data based on the current mode
esp_err_t ble_monitoring_send_simulated_data(ble_monitor_mode_t mode)
{
    static uint32_t counter = 0;
    counter++;

    // // Always update additional sensors data
    // additional_sensors_data_t sensors_data = {
    //     .light = 500 + (counter % 100),  // 模拟光线传感器数据 (500-599 lux)
    //     .pir = (counter / 10) % 2,       // 模拟 PIR 传感器数据 (每10秒切换一次)
    //     .temperature = (int16_t)(470 + (counter % 10)) // 模拟温度数据 (47.0-47.9°C)
    // };
    // ble_monitoring_update_sensors(&sensors_data);

    // Update mode-specific data
    switch (mode) {
        case MONITOR_MODE_VITALS_MONITOR:
            {
                // 模拟心率和呼吸率数据
                vitals_data_t vitals_data = {
                    .heart_rate = 70 + (counter % 10),        // 70-79 bpm
                    .respiratory_rate = 15 + ((counter / 2) % 5) // 15-19 breaths/min
                };
                ble_monitoring_update_vitals(&vitals_data);
            }
            break;

        case MONITOR_MODE_FALL_DETECTION:
            {
                // 模拟跌倒检测数据 (每20秒模拟一次跌倒)
                fall_data_t fall_data = {
                    .status = (counter % 20 == 0) ? 1 : 0
                };
                ble_monitoring_update_fall(&fall_data);
            }
            break;

        case MONITOR_MODE_PRESENCE_DETECTION:
            {
                // 模拟人体存在检测数据
                bool is_present = (counter / 10) % 2;  // 每10秒切换一次状态
                presence_data_t presence_data = {
                    .status = is_present ? 1 : 0,
                    .distance = is_present ? (150 + (counter % 50)) : 0  // 1.5-2.0米
                };
                ble_monitoring_update_presence(&presence_data);
            }
            break;

        default:
            ESP_LOGW(TAG, "Unknown monitoring mode: %d", mode);
            break;
    }

    return ESP_OK;
}
