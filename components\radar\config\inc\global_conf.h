/**
  ******************************************************************************
  * @file    global_conf.h
  * <AUTHOR> team
  * @brief   global config for whole project
  ******************************************************************************
  */
#ifndef __GLOBAL_CONF_H__
#define __GLOBAL_CONF_H__

#ifdef __cplusplus
 extern "C" {
#endif

#include "global_def.h"

/**********function switch*************/
#ifdef EVBKS5_E
#define SUPPORT_DYNAMIC_SYS_MODE
#define SYS_MODE_DEFAULT          SYS_MODE_PASSTHROUGH
#define SUPPORT_DATA_PASSTHROUGH
#endif


/**********para config****************/

#define UPLOAD_SAMPLE_RATE        (1)
#define RADAR_DATA_MAX_LEN        (4096)



#define DEBUG_MODE_DEFAULT        DEBUG_MODE_OFF
#define MCU_PACK_WRAPPER_DEFAULT  (0)                /* 1: 数据封装  0：上电默认MCU不封装*/

 /**********debug**********************/
 //#define CONFIG_DEBUG

#ifdef __cplusplus
}
#endif

#endif


