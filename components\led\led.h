#ifndef LED_H
#define LED_H

#include <stdbool.h>
#include <stdint.h>

#define BLINK_GPIO_COMM 45
#define BLINK_GPIO_CHIRP 41
#define BLINK_GPIO_ERR 42

// LED类型枚举
typedef enum {
    LED_COMM = 0,
    LED_CHIRP,
    LED_ERR
} led_type_t;

// 初始化LED GPIO
void led_init(bool *finish_flag);

// 点亮指定LED（互斥，点亮一个会自动熄灭其他）
void led_on(led_type_t led);

// 熄灭所有LED
void led_off_all(void);

// 反转指定LED（互斥，已亮则灭，未亮则亮并灭其他）
void led_toggle(led_type_t led);

// 轮流点亮三个LED，每次调用点亮下一个
void led_cycle(void);

#endif // LED_H
