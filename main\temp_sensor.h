/**
 * @file temp_sensor.h
 * @brief ESP32-S3内部温度传感器驱动
 * @version 1.0
 * @date 2024-08-10
 */

#ifndef TEMP_SENSOR_H
#define TEMP_SENSOR_H

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 初始化ESP32-S3内部温度传感器
 *
 * @return esp_err_t ESP_OK: 成功, 其他: 失败
 */
esp_err_t temp_sensor_init(void);

/**
 * @brief 反初始化ESP32-S3内部温度传感器
 *
 * @return esp_err_t ESP_OK: 成功, 其他: 失败
 */
esp_err_t temp_sensor_deinit(void);

/**
 * @brief 读取ESP32-S3内部温度传感器的温度值
 *
 * @param temperature 指向存储温度值的浮点数变量的指针
 * @return esp_err_t ESP_OK: 成功, 其他: 失败
 */
esp_err_t read_chip_temperature(float *temperature);

#ifdef __cplusplus
}
#endif

#endif /* TEMP_SENSOR_H */
