/**
 * @file ble_ota_service.h
 * @brief BLE OTA Service Implementation
 * @version 1.0
 * @date 2024-08-10
 */

#ifndef BLE_OTA_SERVICE_H
#define BLE_OTA_SERVICE_H

#include "esp_err.h"
#include "host/ble_uuid.h"
#include "host/ble_hs.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief OTA service UUIDs
 */
// OTA Service UUID: Custom UUID for OTA service
// extern const ble_uuid128_t gatt_ota_svc_uuid;

// 微信小程序使用的OTA服务UUID
extern const ble_uuid128_t gatt_ota_svc_uuid_wx;

// OTA Control Characteristic UUID
extern const ble_uuid128_t gatt_ota_control_chr_uuid;

// OTA Data Characteristic UUID
extern const ble_uuid128_t gatt_ota_data_chr_uuid;

/**
 * @brief OTA control commands
 */
typedef enum {
    OTA_CONTROL_START = 0x01,    // Start OTA process
    OTA_CONTROL_END = 0x02,      // End OTA process
    OTA_CONTROL_ABORT = 0x03,    // Abort OTA process
    OTA_CONTROL_REBOOT = 0x04,   // Reboot device after OTA
    OTA_CONTROL_RESPONSE = 0x05, // Response from device
} ota_control_cmd_t;

/**
 * @brief OTA response codes
 */
typedef enum {
    OTA_RESP_SUCCESS = 0x00,     // Operation successful
    OTA_RESP_ERR_INVALID_CMD = 0x01, // Invalid command
    OTA_RESP_ERR_INVALID_STATE = 0x02, // Invalid state for command
    OTA_RESP_ERR_FLASH_WRITE = 0x03, // Flash write error
    OTA_RESP_ERR_FLASH_READ = 0x04, // Flash read error
    OTA_RESP_ERR_FLASH_ERASE = 0x05, // Flash erase error
    OTA_RESP_ERR_BOOT_PARTITION = 0x06, // Boot partition error
    OTA_RESP_ERR_INVALID_PKT = 0x07, // Invalid packet
    OTA_RESP_ERR_INVALID_CHECKSUM = 0x08, // Invalid checksum
    OTA_RESP_ERR_INVALID_SIZE = 0x09, // Invalid size
    OTA_RESP_ERR_UNKNOWN = 0xFF, // Unknown error
} ota_response_code_t;

/**
 * @brief OTA service callback for GATT registration
 *
 * @param svc_idx Service index
 * @param status Registration status
 * @param app_idx Application index
 */
void ble_ota_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg);

/**
 * @brief Initialize the BLE OTA service
 *
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_ota_service_init(void);

/**
 * @brief Get the current OTA state
 *
 * @return true if OTA is in progress, false otherwise
 */
bool ble_ota_is_in_progress(void);

#ifdef __cplusplus
}
#endif

#endif /* BLE_OTA_SERVICE_H */
