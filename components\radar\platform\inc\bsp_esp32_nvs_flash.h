/*
 * bsp_esp32_nvs_flash.h
 *
 *  Created on: 2023年9月5日
 *      Author: KB402
 */

#ifndef PLATFORM_INC_BSP_ESP32_NVS_FLASH_H_
#define PLATFORM_INC_BSP_ESP32_NVS_FLASH_H_
#include <stdio.h>
#include "esp_err.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "nvs.h"


int bsp_nvs_flash_init(void);
int bsp_nvs_flash_write(char *key, char *value, uint16_t val_len);
int bsp_nvs_flash_read(char *key, char *value, uint16_t val_len);


#endif /* PLATFORM_INC_BSP_ESP32_NVS_FLASH_H_ */
