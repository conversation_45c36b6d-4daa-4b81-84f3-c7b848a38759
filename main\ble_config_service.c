/**
 * @file ble_config_service.c
 * @brief BLE Configuration Service Implementation
 * @version 1.1
 * @date 2024-08-15
 *
 * 注意：命令头已从 0xA5 更改为 0xB5，以避免与 OTA 服务冲突
 * 请在微信小程序中更新 CMD_HEADER 常量为 0xB5
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "host/ble_gatt.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include "ble_config_service.h"
#include "ble_monitoring_service.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "BLE_CONFIG_SERVICE";

// Configuration Service UUID: FFE0 (as specified in the mini-program)
static const ble_uuid16_t gatt_config_svc_uuid = BLE_UUID16_INIT(0xFFE0);

// Configuration Characteristic UUID: FFE1 (as specified in the mini-program)
static const ble_uuid16_t gatt_config_chr_uuid = BLE_UUID16_INIT(0xFFE1);

// Configuration status value
static uint8_t config_status[2] = {0};
static uint16_t config_char_handle;

// Current configuration mode
static ble_config_mode_t current_config_mode = CONFIG_MODE_RAW_DATA; // Default mode

// Forward declaration of access callback
static int ble_config_access(uint16_t conn_handle, uint16_t attr_handle,
                            struct ble_gatt_access_ctxt *ctxt, void *arg);

// GATT Service definition
static const struct ble_gatt_svc_def gatt_config_svcs[] = {
    {
        // Service
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = &gatt_config_svc_uuid.u,
        .characteristics = (struct ble_gatt_chr_def[]) {
            {
                // Configuration Characteristic
                .uuid = &gatt_config_chr_uuid.u,
                .access_cb = ble_config_access,
                .flags = BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &config_char_handle,
            },
            {
                0, // No more characteristics in this service
            }
        },
    },
    {
        0, // No more services
    },
};

// 打印 UUID 为调试目的
static void print_uuid(const ble_uuid_t *uuid)
{
    char buf[BLE_UUID_STR_LEN];
    ESP_LOGI(TAG, "UUID: %s", ble_uuid_to_str(uuid, buf));
}

// Access callback for the configuration service
static int ble_config_access(uint16_t conn_handle, uint16_t attr_handle,
                           struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    int rc;
    uint8_t response[2];

    ESP_LOGI(TAG, "配置服务访问回调被调用，操作: %d, 句柄: %d", ctxt->op, attr_handle);

    switch (ctxt->op) {
        case BLE_GATT_ACCESS_OP_READ_CHR:
            if (attr_handle == config_char_handle) {
                ESP_LOGI(TAG, "Read configuration status: mode=%d", current_config_mode);
                rc = os_mbuf_append(ctxt->om, config_status, sizeof(config_status));
                return rc == 0 ? 0 : BLE_ATT_ERR_INSUFFICIENT_RES;
            }
            break;

        case BLE_GATT_ACCESS_OP_WRITE_CHR:
            if (attr_handle == config_char_handle) {
                uint16_t len;

                // Get the data from the request
                rc = ble_hs_mbuf_to_flat(ctxt->om, config_status, sizeof(config_status), &len);
                if (rc != 0 || len < 2) {
                    ESP_LOGE(TAG, "Invalid configuration data received");
                    return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
                }

                ESP_LOGI(TAG, "收到配置数据: [0x%02x, 0x%02x], 长度: %d",
                         config_status[0], config_status[1], len);

                // Check command header
                if (config_status[0] != CONFIG_CMD_HEADER) {
                    ESP_LOGE(TAG, "Invalid command header: 0x%02x", config_status[0]);
                    return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
                }

                // Process the configuration command
                ble_config_mode_t new_mode = (ble_config_mode_t)config_status[1];
                ESP_LOGI(TAG, "Received configuration command: 0x%02x (mode=%d)", config_status[0], new_mode);

                // Update the current mode
                current_config_mode = new_mode;

                // Prepare response
                response[0] = CONFIG_CMD_HEADER;
                response[1] = CONFIG_RESPONSE_SUCCESS;

                // Update the config status with response
                config_status[0] = response[0];
                config_status[1] = response[1];

                // 尝试多种方式发送通知
                ESP_LOGI(TAG, "正在发送响应通知...");

                // 方法1：使用 ble_gatts_chr_updated
                ble_gatts_chr_updated(config_char_handle);

                // 方法2：使用 ble_gattc_notify (如果可用)
                int rc2 = ble_gattc_notify(conn_handle, config_char_handle);
                ESP_LOGI(TAG, "ble_gattc_notify 结果: %d", rc2);

                // 方法3：使用自定义通知 (如果可用)
                struct os_mbuf *om = ble_hs_mbuf_from_flat(response, sizeof(response));
                if (om) {
                    int rc3 = ble_gattc_notify_custom(conn_handle, config_char_handle, om);
                    ESP_LOGI(TAG, "ble_gattc_notify_custom 结果: %d", rc3);
                }

                ESP_LOGI(TAG, "已发送成功响应通知");

                return 0;
            }
            break;

        default:
            break;
    }

    // Unknown characteristic
    return BLE_ATT_ERR_UNLIKELY;
}

// Initialize the configuration service
esp_err_t ble_config_service_init(void)
{
    int rc;

    // 打印服务和特征值 UUID 以便调试
    char buf1[BLE_UUID_STR_LEN], buf2[BLE_UUID_STR_LEN];
    ESP_LOGI(TAG, "配置服务 UUID: %s", ble_uuid_to_str(&gatt_config_svc_uuid.u, buf1));
    ESP_LOGI(TAG, "配置特征值 UUID: %s", ble_uuid_to_str(&gatt_config_chr_uuid.u, buf2));

    // Add the service to the BLE stack
    rc = ble_gatts_count_cfg(gatt_config_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to count GATT service config: %d", rc);
        return ESP_FAIL;
    }

    rc = ble_gatts_add_svcs(gatt_config_svcs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to add GATT service: %d", rc);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "配置服务初始化成功，特征值句柄: %d", config_char_handle);
    return ESP_OK;
}

// Get the current configuration mode
ble_config_mode_t ble_config_get_current_mode(void)
{
    return current_config_mode;
}

// Callback for GATT service registration
void ble_config_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg)
{
    char buf[BLE_UUID_STR_LEN];

    switch (ctxt->op) {
        case BLE_GATT_REGISTER_OP_SVC:
            ESP_LOGI(TAG, "Registered service %s with handle=%d",
                     ble_uuid_to_str(ctxt->svc.svc_def->uuid, buf), ctxt->svc.handle);
            break;

        case BLE_GATT_REGISTER_OP_CHR:
            ESP_LOGI(TAG, "Registered characteristic %s with def_handle=%d val_handle=%d",
                     ble_uuid_to_str(ctxt->chr.chr_def->uuid, buf),
                     ctxt->chr.def_handle, ctxt->chr.val_handle);
            break;

        default:
            break;
    }
}

// Send simulated data based on the current mode
esp_err_t ble_config_send_simulated_data(void)
{
    // 使用新的监测服务发送模拟数据
    ble_monitor_mode_t monitor_mode = (ble_monitor_mode_t)current_config_mode;
    return ble_monitoring_send_simulated_data(monitor_mode);
}
