/**
  ******************************************************************************
  * @file           : banyan.c
  * <AUTHOR> iclm team
  * @brief          : banyan driver
  ******************************************************************************
  */
#include <stdio.h>
#include <math.h>
#include "../../config/inc/global_conf.h"
#include "../../config/inc/radar_config.h"
#include "platform.h"
#include "banyan.h"
// #include "banyan_para.c"
#include "banyan_para_custom.c"

#ifdef ESP32_PLATFORM
#include "esp_log.h"
#endif

const char *TAG = "banyan.c";
uint8_t g_TxCount = (CHANNEL_MAX + 1) / 2;

uint8_t g_ChipCount = (CHANNEL_MAX + 1) / 2;
uint8_t g_RxCount = CHANNEL_MAX;

static uint16_t rawPointMap[RAW_MAP_NUM] =
{
	RAW_POINT_64,
    RAW_POINT_128,
    RAW_POINT_256,
    RAW_POINT_512,
    RAW_POINT_1024
};


static const RADAR_REG_T RegList_LowPWR[] =
{
	{0x70, 0x1020},
	{0x6C, 0x8880},
	{0x6D, 0x8800},
	{0x72, 0x0650},
	{0x67, 0x0000},
	{0x66, 0xF0F0},
	{0x6E, 0x03FC},
	{0x41, 0x4804},
	{0x00, 0x0000}
};

static const RADAR_REG_T RegList_NormalPWR[] =
{
	{0x41, 0xc864},
	{0x72, 0x0653},
	{0x6C, 0x9990},
	{0x6D, 0x9940},
	{0x70, 0x32a0},
	{0x6E, 0xabFC},
	{0x66, 0x0a00},
	{0x67, 0x1840},
	{0x00, 0x0000}
};

const static RADAR_REG_T InitChipRegListStop_BanyanE[9] __attribute__((aligned (4))) =
{

    {0x40, 0x4207},
    {0x41, 0x0000},
    {0x09, 0xE901},
    {0x01, 0x0000},
    {0x67, 0x0000},
    {0x72, 0x0650},
    {0x3A, 0x8410},
    {0x77, 0x3200},
    {0xFF, 0xFFFF} /*must be last, do not delete!!!*/
};


void Radar_Init_Stop_BanyanE(void)
{
    uint16_t loop = 0;

    loop = 0;
    while(((InitChipRegListStop_BanyanE[loop].addr != 0xFF) || (InitChipRegListStop_BanyanE[loop].val != 0xFFFF)))
    {
        if(bsp_iic_write(I2C_ADDR_BanYan_Chip0, (uint8_t)(InitChipRegListStop_BanyanE[loop].addr), InitChipRegListStop_BanyanE[loop].val) != ESP_OK)
        {
        }
        loop++;
    }

    loop = 0;
    while(((InitChipRegListConfig0[loop].addr != 0xFF) || (InitChipRegListConfig0[loop].val != 0xFFFF)))
    {
        bsp_iic_write(I2C_ADDR_BanYan_Chip0, (uint8_t)(InitChipRegListConfig0[loop].addr), InitChipRegListConfig0[loop].val);
        //添加打印信息
        // ESP_LOGI(TAG, "Radar_Init_Stop_BanyanE chip0: addr=0x%02x val=0x%02x\r\n", InitChipRegListConfig0[loop].addr, InitChipRegListConfig0[loop].val);
        loop++;
    }
}

void Radar_Init_Start_BanyanE(void)
{
    uint16_t loop = 0;
    while(((InitChipRegListStart0[loop].addr != 0xFF) || (InitChipRegListStart0[loop].val != 0xFFFF)))
    {
        bsp_iic_write(I2C_ADDR_BanYan_Chip0, (uint8_t)(InitChipRegListStart0[loop].addr), InitChipRegListStart0[loop].val);
        //添加打印信息
        // ESP_LOGI(TAG, "Radar_Init_Start_BanyanE chip0: addr=0x%02x val=0x%02x\r\n", InitChipRegListStart0[loop].addr, InitChipRegListStart0[loop].val);
        loop++;
    }

}

uint8_t Radar_GetRxGainType(void)
{
    uint8_t valRet = 0;
    uint16_t valLpf = 0;
    uint16_t valLna = 0;
    uint16_t bAutoMode = 0;

    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_FUN_SWITCH, &bAutoMode);
    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_LPF_CHANNEL_ENABLE, &valLpf);
    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_LNA_CHANNEL_ENABLE, &valLna);

    if((bAutoMode & 0x180) == 0x100)
    {
        valRet |= 1;
    }
    else if((bAutoMode & 0x180) == 0x180)
    {
        valRet |= 3;
    }
    else
    {
        if((((valLpf >> 8) & 0xa) | ((valLna >> 14) & 0x2)) != 0)
        {
            valRet |= 1;
        }

        if((((valLpf >> 8) & 0x5) | ((valLna >> 14) & 0x1)) != 0)
        {
            valRet |= 2;
        }
    }

//    if(bsp_iic_read(I2C_ADDR_BanYan_Chip1, BANYAN_DIG_FUN_SWITCH, &bAutoMode) == HAL_OK)
//    {
//        if((bAutoMode & 0x180) == 0x100)
//        {
//            valRet |= 4;
//        }
//        else if((bAutoMode & 0x180) == 0x180)
//        {
//            valRet |= 0xc;
//        }
//        else
//        {
//            if(bsp_iic_read(I2C_ADDR_BanYan_Chip1, BANYAN_LPF_CHANNEL_ENABLE, &valLpf) == HAL_OK)
//            {
//                bsp_iic_read(I2C_ADDR_BanYan_Chip1, BANYAN_LNA_CHANNEL_ENABLE, &valLna);
//                if((((valLpf >> 8) & 0xa) | ((valLna >> 14) & 0x2)) != 0)
//                {
//                    valRet |= 4;
//                }
//
//                if((((valLpf >> 8) & 0x5) | ((valLna >> 14) & 0x1)) != 0)
//                {
//                    valRet |= 8;
//                }
//            }
//        }
//    }

    return (valRet == 0) ? 3 : valRet;
}

uint16_t Radar_GetFftPoint(void)
{
	uint16_t regVal = 64;
    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_FFT_NUM, &regVal);
	return regVal;
}

uint16_t Radar_GetRawPoint(void)
{
    uint16_t val = 256;
    uint16_t rawVal = 0;

    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_RAW_PEAK_NUM, &val);
    rawVal = (val >> BANYAN_RAW_POS) & BANYAN_RAW_MASK;

    if (rawVal < RAW_MAP_NUM)
    {
        return rawPointMap[rawVal];
    }
    else
    {
        return RAW_POINT_64;
    }
}

uint16_t Radar_GetOneFrameChirpNum(void)
{
    uint16_t val = 32;
    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_PAT_CHIRP_NUM, &val);
    val &= 0x1FF;
    return val;
}

uint16_t Radar_GetDfftRoiEnable(void)
{
    uint16_t roiValue = 0;
    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_DFFT_ROI, &roiValue);
    if(roiValue & BANYAN_DIG_DFFT_ROI)
    {
        return 0;
    }
    else
    {
        return 1;
    }

}
uint16_t Radar_GetDfftDataNum(void)
{
    uint16_t val = 0x2000;

    uint16_t valRet = 0;
    if(Radar_GetDfftRoiEnable())
    {
        bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_DFFT_ROI_DATA_NUM, &val);
        valRet = (val >> BANYAN_DFFT_DATA_NUM_POS) + 1;
    }
    else
    {
        bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_DFFT_DATA_NUM, &val);
        valRet = val & 0xFF;
    }

    return valRet;
}

uint16_t Radar_GetDfftPeakSize(void)
{
    uint16_t val = 32;

    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_RAW_PEAK_NUM, &val);

    return ((val & BANYAN_PEAK_MASK) * 4); /*4--word length*/
}

uint16_t Radar_GetDfftChirpNum(void)
{
    uint16_t val = 0x2000;

    uint16_t valRet = 0;
    if(Radar_GetDfftRoiEnable())
    {
        bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_DFFT_ROI_CHIRP_NUM, &val);
        valRet = (val >> BANYAN_DFFT_DATA_NUM_POS) + 1;
    }
    else
    {
        bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_DFFT_CHIRP_NUM, &val);
        valRet = val & 0xFF;
    }

    return valRet;
}

uint8_t Radar_GetDataType(void)
{
    uint8_t dataType = DATA_TYPE_FFT;
    uint16_t val = BANYAN_FFT_DATA;

    bsp_iic_read(I2C_ADDR_BanYan_Chip0, BANYAN_DIG_FUN_SWITCH, &val);

    if (val & BANYAN_DFFT_DATA)
    {
        dataType = DATA_TYPE_DFFT;
    }
    else if (val & BANYAN_FFT_DATA)
    {
        dataType = DATA_TYPE_FFT;
    }
    else if (val & BANYAN_DFFT_PEAK_DATA)
    {
        dataType = DATA_TYPE_DFFT_PEAK;
    }
	else if (val & BANYAN_DSRAW_DATA)
    {
        dataType = DATA_TYPE_DSRAW;
    }
    else
    {
        dataType = DATA_TYPE_MAX;
    }

    return dataType;
}

uint8_t Radar_GetDataTypeByRegisterList(void)
{
    uint8_t dataType = DATA_TYPE_FFT;
    uint16_t val = BANYAN_FFT_DATA;

    uint16_t loop = 0;
    while(((InitChipRegListStart0[loop].addr != 0xFF) || (InitChipRegListStart0[loop].val != 0xFFFF)))
    {
		if((uint8_t)InitChipRegListStart0[loop].addr == BANYAN_DIG_FUN_SWITCH)
		{
			val = InitChipRegListStart0[loop].val;
		}
        loop++;
    }

    if (val & BANYAN_DFFT_DATA)
    {
        dataType = DATA_TYPE_DFFT;
    }
    else if (val & BANYAN_FFT_DATA)
    {
        dataType = DATA_TYPE_FFT;
    }
    else if (val & BANYAN_DFFT_PEAK_DATA)
    {
        dataType = DATA_TYPE_DFFT_PEAK;
    }
	else if (val & BANYAN_DSRAW_DATA)
    {
        dataType = DATA_TYPE_DSRAW;
    }
    else
    {
        dataType = DATA_TYPE_MAX;
    }

    return dataType;
}

void Radar_Enter_LowPWR(uint16_t devAddr)
{
    uint16_t loop = 0;
    while((RegList_LowPWR[loop].addr) || (RegList_LowPWR[loop].val != 0x0000))
    {
        bsp_iic_write(devAddr, (uint8_t)(RegList_LowPWR[loop].addr), RegList_LowPWR[loop].val);
        loop++;
    }
}

void Radar_Exit_LowPWR(uint16_t devAddr)
{
    uint16_t loop = 0;
    while((RegList_NormalPWR[loop].addr) || (RegList_NormalPWR[loop].val != 0x0000))
    {
        bsp_iic_write(devAddr, (uint8_t)(RegList_NormalPWR[loop].addr), RegList_NormalPWR[loop].val);
        loop++;
    }
}


//void Radar_Init(void)
//{
//    uint16_t loop = 0;

//#ifdef CONFIG_DEBUG
//    ESP_LOGI(TAG,"radar flash value:\r\n");
//    while(InitRegList[loop].addr)
//    {
//        ESP_LOGI(TAG,"%02X=%04X\r\n", InitRegList[loop].addr, InitRegList[loop].val);
//        loop++;
//    }
//    loop = 0;
//#endif

//
//    while((InitRegList[loop].addr || ((InitRegList[loop].addr == 0x00) && (InitRegList[loop+1].addr == 0x01))))
//    {
//        bsp_iic_write(I2C_ADDR_BanYan_Chip0, (uint8_t)(InitRegList[loop].addr), InitRegList[loop].val);
//        loop++;
//
//    }

//#ifdef CONFIG_DEBUG
//    loop = 0;
//    ESP_LOGI(TAG,"radar ic value:\r\n");
//	  uint16_t val = 0;
//    while(InitRegList[loop].addr)
//    {
//		    bsp_iic_read(I2C_ADDR_BanYan_Chip0, InitRegList[loop].addr, &val);
//        ESP_LOGI(TAG,"%02X=%04X\r\n", InitRegList[loop].addr, val);
//        loop++;
//    }
//#endif
//}


void Radar_UpdateReg(uint16_t devAddr, uint16_t addr, uint16_t val)/*currently only update existing reg*/
{
    uint16_t loop = 0;
    RADAR_REG_T *pRegConfig = NULL;
    RADAR_REG_T *pRegStart = NULL;

    if(devAddr == I2C_ADDR_BanYan_Chip0)
    {
        pRegConfig = InitChipRegListConfig0;
        pRegStart = InitChipRegListStart0;
    }
    else
    {
        return;
    }

    while (!((pRegConfig[loop].addr == 0xFF) && (pRegConfig[loop].val == 0xFFFF)))
    {
        if (pRegConfig[loop].addr == addr)
        {
            pRegConfig[loop].val = val;
            Config_NeedFlashWrite();
            return;
        }
        loop++;
    }

    loop = 0;
    while (!((pRegStart[loop].addr == 0xFF) && (pRegStart[loop].val == 0xFFFF)))
    {
        if (pRegStart[loop].addr == addr)
        {
            pRegStart[loop].val = val;
            Config_NeedFlashWrite();
            return;
        }
        loop++;
    }
}

void* Radar_GetChipRegListConfigParaAddr(uint8_t chip)
{
    return (void*)&InitChipRegListConfig0;
}

uint32_t Radar_GetChipRegListConfigParaLen(uint8_t chip)
{
    return sizeof(InitChipRegListConfig0);
}

void* Radar_GetChipRegListStartParaAddr(uint8_t chip)
{
    return (void*)&InitChipRegListStart0;
}

uint32_t Radar_GetChipRegListStartParaLen(uint8_t chip)
{
    return sizeof(InitChipRegListStart0);
}

/**
 * @brief 读取芯片温度
 * @param chipAddr 芯片地址
 * @return 温度值（摄氏度），如果读取失败返回-1000.0
 * @note 温度计算公式：temp_value = -0.746 * temp_code + 506.716
 */
float Banyan_ReadTemperature(uint8_t chipAddr)
{
    uint16_t reg77 = 0;
    uint16_t reg71 = 0;
    uint16_t reg73 = 0;
    uint16_t regTemp = 0;
    float temperature = -1000.0f; // 默认错误值

    // 步骤1: 读取并修改寄存器0x77
    if (bsp_iic_read(chipAddr * 2, 0x77, &reg77) != ESP_OK) {
        ESP_LOGE("banyan", "读取寄存器0x77失败");
        return temperature;
    }

    regTemp = reg77;
    regTemp |= 0x500; // 设置bit8和bit10

    if (bsp_iic_write(chipAddr * 2, 0x77, regTemp) != ESP_OK) {
        ESP_LOGE("banyan", "写入寄存器0x77失败");
        return temperature;
    }

    // 步骤2: 读取寄存器0x71的原始值
    if (bsp_iic_read(chipAddr * 2, 0x71, &reg71) != ESP_OK) {
        ESP_LOGE("banyan", "读取寄存器0x71失败");
        // 恢复寄存器0x77
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return temperature;
    }

    // 步骤3: 写入0x5021到寄存器0x71
    if (bsp_iic_write(chipAddr * 2, 0x71, 0x5021) != ESP_OK) {
        ESP_LOGE("banyan", "写入寄存器0x71(0x5021)失败");
        // 恢复寄存器0x77
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return temperature;
    }

    // 步骤4: 写入0x5821到寄存器0x71
    if (bsp_iic_write(chipAddr * 2, 0x71, 0x5821) != ESP_OK) {
        ESP_LOGE("banyan", "写入寄存器0x71(0x5821)失败");
        // 恢复寄存器
        bsp_iic_write(chipAddr * 2, 0x71, reg71);
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return temperature;
    }

    // 步骤5: 读取寄存器0x73获取温度代码
    if (bsp_iic_read(chipAddr * 2, 0x73, &reg73) != ESP_OK) {
        ESP_LOGE("banyan", "读取寄存器0x73失败");
        // 恢复寄存器
        bsp_iic_write(chipAddr * 2, 0x71, reg71);
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return temperature;
    }

    // 步骤6: 检查数据有效性
    if ((reg73 & 0x800) == 0) {
        ESP_LOGW("banyan", "温度数据无效");
        // 恢复寄存器
        bsp_iic_write(chipAddr * 2, 0x71, reg71);
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return temperature;
    }

    // 步骤7: 恢复寄存器0x71的原始值
    if (bsp_iic_write(chipAddr * 2, 0x71, reg71) != ESP_OK) {
        ESP_LOGE("banyan", "恢复寄存器0x71失败");
        // 恢复寄存器0x77
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return temperature;
    }

    // 步骤8: 计算温度值
    uint16_t temp_code = reg73 & 0x03FF;
    temperature = -0.746f * temp_code + 506.716f;

    ESP_LOGI("banyan", "芯片[0x%02X]温度: %.2f°C (代码: 0x%04X)", chipAddr, temperature, temp_code);

    return temperature;
}

/**
 * @brief 读取芯片功率
 * @param chipAddr 芯片地址
 * @return 功率值，如果读取失败返回0
 */
uint16_t Banyan_ReadPower(uint8_t chipAddr)
{
    uint16_t reg77 = 0;
    uint16_t reg71 = 0;
    uint16_t reg73 = 0;
    uint16_t regTemp = 0;
    uint16_t power = 0; // 默认错误值

    // 步骤1: 读取并修改寄存器0x77
    if (bsp_iic_read(chipAddr * 2, 0x77, &reg77) != ESP_OK) {
        ESP_LOGE("banyan", "读取寄存器0x77失败");
        return power;
    }

    regTemp = reg77;
    regTemp |= 0x500; // 设置bit8和bit10

    if (bsp_iic_write(chipAddr * 2, 0x77, regTemp) != ESP_OK) {
        ESP_LOGE("banyan", "写入寄存器0x77失败");
        return power;
    }

    // 步骤2: 读取寄存器0x71的原始值
    if (bsp_iic_read(chipAddr * 2, 0x71, &reg71) != ESP_OK) {
        ESP_LOGE("banyan", "读取寄存器0x71失败");
        // 恢复寄存器0x77
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return power;
    }

    // 步骤3: 写入0xE021到寄存器0x71
    if (bsp_iic_write(chipAddr * 2, 0x71, 0xE021) != ESP_OK) {
        ESP_LOGE("banyan", "写入寄存器0x71(0xE021)失败");
        // 恢复寄存器0x77
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return power;
    }

    // 步骤4: 写入0xE821到寄存器0x71
    if (bsp_iic_write(chipAddr * 2, 0x71, 0xE821) != ESP_OK) {
        ESP_LOGE("banyan", "写入寄存器0x71(0xE821)失败");
        // 恢复寄存器
        bsp_iic_write(chipAddr * 2, 0x71, reg71);
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return power;
    }

    // 步骤5: 读取寄存器0x73获取功率代码
    if (bsp_iic_read(chipAddr * 2, 0x73, &reg73) != ESP_OK) {
        ESP_LOGE("banyan", "读取寄存器0x73失败");
        // 恢复寄存器
        bsp_iic_write(chipAddr * 2, 0x71, reg71);
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return power;
    }

    // 步骤6: 检查数据有效性
    if ((reg73 & 0x800) == 0) {
        ESP_LOGW("banyan", "功率数据无效");
        // 恢复寄存器
        bsp_iic_write(chipAddr * 2, 0x71, reg71);
        bsp_iic_write(chipAddr * 2, 0x77, reg77);
        return power;
    }

    // 步骤7: 恢复寄存器0x71和0x77的原始值
    if (bsp_iic_write(chipAddr * 2, 0x71, reg71) != ESP_OK) {
        ESP_LOGE("banyan", "恢复寄存器0x71失败");
    }

    if (bsp_iic_write(chipAddr * 2, 0x77, reg77) != ESP_OK) {
        ESP_LOGE("banyan", "恢复寄存器0x77失败");
    }

    // 步骤8: 获取功率值
    power = reg73 & 0x03FF; // 取低10位作为功率值

    ESP_LOGI("banyan", "芯片[0x%02X]功率: %u (代码: 0x%04X)", chipAddr, power, reg73);

    return power;
}

