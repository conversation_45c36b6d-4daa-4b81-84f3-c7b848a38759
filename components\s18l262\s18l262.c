/**
 * @file s18l262.c
 * @brief S18-L262B-2人体感应传感器驱动实现
 * @version 0.3 // 版本更新
 * @date 2024-07-28 // 修改日期
 */

#include "s18l262.h"
#include <string.h>
#include "esp_log.h"
#include "esp_idf_version.h"
#include "freertos/FreeRTOS.h" // 用户原始包含，保留
#include "freertos/task.h"     // 用户原始包含，保留
#include "driver/gpio.h"
#include "driver/ledc.h"
#include <inttypes.h> // For PRIu16, PRIu32

static const char *TAG = "S18L262";

// 静态变量
static int s_pir_rel_pin = -1; // 使用-1表示未初始化，以便init函数中正确处理默认值
static int s_dac_set_ontime_pin = -1;
static int s_dac_set_sens_pin = -1;
static bool s_use_pwm = false;
static bool s_is_initialized = false; // 用于跟踪驱动是否已初始化
static bool s_is_sensor_enabled = false; // 用于跟踪传感器是否通过 enable/disable 函数启用
static s18l262_intr_type_t s_intr_type = S18L262_INTR_NONE;
static s18l262_intr_handler_t s_intr_handler = NULL;
static void* s_intr_handler_arg = NULL;
static bool s_is_isr_service_installed_by_us = false; // 跟踪此驱动是否安装了ISR服务

// 中断服务函数
static void IRAM_ATTR s18l262_isr_handler_wrapper(void* arg)
{
    // 调用用户注册的中断处理函数
    if (s_intr_handler) {
        s_intr_handler(s_intr_handler_arg);
    }
}

esp_err_t s18l262_init(const s18l262_config_t* config)
{
    if (s_is_initialized) {
        ESP_LOGW(TAG, "Fail: 驱动已初始化。如果需要重新配置，请先反初始化。");
        return ESP_ERR_INVALID_STATE;
    }
    if (config == NULL) {
        ESP_LOGE(TAG, "Fail: 配置指针为空");
        return ESP_ERR_INVALID_ARG;
    }

    // 保存配置, 如果配置中小于0则使用头文件默认值
    s_pir_rel_pin = config->pir_rel_pin >= 0 ? config->pir_rel_pin : S18L262_PIR_REL_PIN;
    s_dac_set_ontime_pin = config->dac_set_ontime_pin >= 0 ? config->dac_set_ontime_pin : S18L262_DAC_SET_ONTIME_PIN;
    s_dac_set_sens_pin = config->dac_set_sens_pin >= 0 ? config->dac_set_sens_pin : S18L262_DAC_SET_SENS_PIN;
    s_use_pwm = config->use_pwm;
    s_intr_type = config->intr_type;

    ESP_LOGI(TAG, "初始化S18L262传感器: PIR_REL=%d, ONTIME_PIN=%d, SENS_PIN=%d, use_pwm=%d, intr_type=%d",
             s_pir_rel_pin, s_dac_set_ontime_pin, s_dac_set_sens_pin, s_use_pwm, s_intr_type);

    // 配置PIR_REL引脚为输入
    gpio_config_t io_conf_pir = {
        .pin_bit_mask = (1ULL << s_pir_rel_pin),
        .mode = GPIO_MODE_INPUT,
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_ENABLE, // 建议启用下拉，确保REL低电平时的稳定状态
        .intr_type = GPIO_INTR_DISABLE,       // 初始时禁用中断
    };
    esp_err_t ret = gpio_config(&io_conf_pir);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置PIR_REL引脚 %d 失败: %s", s_pir_rel_pin, esp_err_to_name(ret));
        return ret;
    }

    // 如果使用PWM模拟DAC
    if (s_use_pwm) {
        ESP_LOGI(TAG, "使用PWM模式配置ONTIME和SENS引脚");
        // 配置LEDC定时器
        ledc_timer_config_t timer_conf = {
            .speed_mode = LEDC_LOW_SPEED_MODE,
            .timer_num = S18L262_PWM_TIMER,
            .duty_resolution = S18L262_PWM_RESOLUTION,
            .freq_hz = S18L262_PWM_FREQ,
            .clk_cfg = LEDC_AUTO_CLK,
        };
        ret = ledc_timer_config(&timer_conf);
        if (ret != ESP_OK) {
            // ESP_ERR_INVALID_STATE 表示定时器已被其他地方以相同配置初始化，可以接受
            if (ret == ESP_ERR_INVALID_STATE) {
                 ESP_LOGW(TAG, "LEDC 定时器 %d 可能已被其他模块配置，或重复初始化。", S18L262_PWM_TIMER);
            } else {
                ESP_LOGE(TAG, "配置LEDC定时器 %d 失败: %s", S18L262_PWM_TIMER, esp_err_to_name(ret));
                return ret;
            }
        } else {
             ESP_LOGI(TAG, "LEDC 定时器 %d 配置成功。", S18L262_PWM_TIMER);
        }


        // 配置ONTIME通道
        ledc_channel_config_t ontime_conf = {
            .speed_mode = LEDC_LOW_SPEED_MODE,
            .channel = S18L262_ONTIME_CHANNEL,
            .timer_sel = S18L262_PWM_TIMER,
            .intr_type = LEDC_INTR_DISABLE,
            .gpio_num = s_dac_set_ontime_pin,
            .duty = 0, // 初始占空比为0
            .hpoint = 0,
        };
        ret = ledc_channel_config(&ontime_conf);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "配置ONTIME PWM通道 %d (GPIO %d) 失败: %s", S18L262_ONTIME_CHANNEL, s_dac_set_ontime_pin, esp_err_to_name(ret));
            return ret;
        }

        // 配置SENS通道
        ledc_channel_config_t sens_conf = {
            .speed_mode = LEDC_LOW_SPEED_MODE,
            .channel = S18L262_SENS_CHANNEL,
            .timer_sel = S18L262_PWM_TIMER,
            .intr_type = LEDC_INTR_DISABLE,
            .gpio_num = s_dac_set_sens_pin,
            .duty = 0, // 初始占空比为0
            .hpoint = 0,
        };
        ret = ledc_channel_config(&sens_conf);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "配置SENS PWM通道 %d (GPIO %d) 失败: %s", S18L262_SENS_CHANNEL, s_dac_set_sens_pin, esp_err_to_name(ret));
            return ret;
        }
        ESP_LOGI(TAG, "PWM通道为ONTIME (GPIO %d) 和 SENS (GPIO %d) 配置完成。", s_dac_set_ontime_pin, s_dac_set_sens_pin);
    } else {
        ESP_LOGI(TAG, "使用GPIO模式配置ONTIME和SENS引脚 (拉高)");
        // 配置DAC_SET_ONTIME和DAC_SET_SENS引脚为输出
        gpio_config_t io_conf_dac = {0}; // 初始化结构体
        io_conf_dac.pin_bit_mask = (1ULL << s_dac_set_ontime_pin) | (1ULL << s_dac_set_sens_pin);
        io_conf_dac.mode = GPIO_MODE_OUTPUT;
        io_conf_dac.pull_up_en = GPIO_PULLUP_DISABLE;
        io_conf_dac.pull_down_en = GPIO_PULLDOWN_DISABLE;
        io_conf_dac.intr_type = GPIO_INTR_DISABLE;
        ret = gpio_config(&io_conf_dac);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "配置DAC GPIO引脚失败: %s", esp_err_to_name(ret));
            return ret;
        }

        // 设置初始电平为高
        gpio_set_level((gpio_num_t)s_dac_set_ontime_pin, 0);
        gpio_set_level((gpio_num_t)s_dac_set_sens_pin, 0);
        ESP_LOGI(TAG, "ONTIME (GPIO %d) 和 SENS (GPIO %d) 引脚已拉高。", s_dac_set_ontime_pin, s_dac_set_sens_pin);
    }

    // 如果需要中断
    if (s_intr_type != S18L262_INTR_NONE) {
        gpio_int_type_t intr_type_gpio;
        switch (s_intr_type) {
            case S18L262_INTR_RISING:  intr_type_gpio = GPIO_INTR_POSEDGE; break;
            case S18L262_INTR_FALLING: intr_type_gpio = GPIO_INTR_NEGEDGE; break;
            case S18L262_INTR_ANY:     intr_type_gpio = GPIO_INTR_ANYEDGE; break;
            default:                   intr_type_gpio = GPIO_INTR_DISABLE; break;
        }

        if (intr_type_gpio != GPIO_INTR_DISABLE) {
            ret = gpio_set_intr_type((gpio_num_t)s_pir_rel_pin, intr_type_gpio);
            if (ret != ESP_OK) {
                ESP_LOGE(TAG, "设置PIR_REL引脚 %d 中断类型失败: %s", s_pir_rel_pin, esp_err_to_name(ret));
                return ret;
            }

            // 安装GPIO中断服务 (如果尚未安装)
            // ESP-IDF v4.x 及更早版本使用 0 作为 flags，v5.x 及以后可以使用 ESP_INTR_FLAG_IRAM 等
            #if ESP_IDF_VERSION >= ESP_IDF_VERSION_VAL(5, 0, 0)
            esp_err_t isr_service_ret = gpio_install_isr_service(ESP_INTR_FLAG_LEVEL3); // 使用一个合适的flag
            #else
            esp_err_t isr_service_ret = gpio_install_isr_service(0);
            #endif

            if (isr_service_ret == ESP_OK) {
                s_is_isr_service_installed_by_us = true; // 标记是我们安装的
                ESP_LOGI(TAG, "GPIO中断服务已安装。");
            } else if (isr_service_ret == ESP_ERR_INVALID_STATE) {
                ESP_LOGW(TAG, "GPIO中断服务已由其他模块安装。");
                s_is_isr_service_installed_by_us = false; // 不是我们安装的
            } else {
                ESP_LOGE(TAG, "安装GPIO中断服务失败: %s", esp_err_to_name(isr_service_ret));
                return isr_service_ret;
            }

            // 添加中断处理函数
            ret = gpio_isr_handler_add((gpio_num_t)s_pir_rel_pin, s18l262_isr_handler_wrapper, (void*)(uintptr_t)s_pir_rel_pin);
            if (ret != ESP_OK) {
                ESP_LOGE(TAG, "为PIR_REL引脚 %d 添加中断处理函数失败: %s", s_pir_rel_pin, esp_err_to_name(ret));
                if (s_is_isr_service_installed_by_us) { // 如果是我们安装的，则卸载
                    gpio_uninstall_isr_service();
                    s_is_isr_service_installed_by_us = false;
                }
                return ret;
            }
            ESP_LOGI(TAG, "PIR_REL引脚 %d 的中断处理函数已添加。", s_pir_rel_pin);
        }
    }

    s_is_initialized = true;
    s_is_sensor_enabled = false; // 初始时传感器功能通过 s18l262_enable() 启用

    s18l262_enable();


    if (s_use_pwm) {
        uint16_t target_ontime_seconds = 60;
        ESP_LOGI(TAG, "PWM模式：尝试设置延时时间为 %u 秒...", target_ontime_seconds);
        esp_err_t ontime_ret = s18l262_set_ontime(target_ontime_seconds);
        if (ontime_ret == ESP_OK) {
            ESP_LOGI(TAG, "延时时间设置成功 (目标 %u 秒)。", target_ontime_seconds);
        } else {
            ESP_LOGE(TAG, "设置延时时间失败: %s", esp_err_to_name(ontime_ret));
        }

        uint8_t target_sensitivity_percent = 75;
        ESP_LOGI(TAG, "PWM模式：尝试设置灵敏度为 %u%%...", target_sensitivity_percent);
        esp_err_t sens_ret = s18l262_set_sensitivity(target_sensitivity_percent);
        if (sens_ret == ESP_OK) {
            ESP_LOGI(TAG, "灵敏度设置成功 (目标 %u%%)。", target_sensitivity_percent);
        } else {
            ESP_LOGE(TAG, "设置灵敏度失败: %s", esp_err_to_name(sens_ret));
        }
    }
    ESP_LOGI(TAG, "S18L262传感器驱动初始化成功。调用 s18l262_enable() 来启用传感器功能。");
    return ESP_OK;
}

esp_err_t s18l262_set_intr_handler(s18l262_intr_handler_t handler, void* arg)
{
    if (!s_is_initialized) {
        ESP_LOGE(TAG, "驱动未初始化，无法设置中断处理函数。");
        return ESP_ERR_INVALID_STATE;
    }
    if (s_intr_type == S18L262_INTR_NONE) {
        ESP_LOGW(TAG, "中断类型未配置 (S18L262_INTR_NONE)，设置处理函数可能无效。");
    }

    s_intr_handler = handler;
    s_intr_handler_arg = arg;
    ESP_LOGI(TAG, "中断处理函数已 %s。", handler ? "设置" : "清除");
    return ESP_OK;
}

bool s18l262_read_state(void)
{
    if (!s_is_initialized) {
        ESP_LOGW(TAG, "驱动未初始化，读取状态可能不准确。");
        return false;
    }
    // s_is_sensor_enabled 的检查可以根据需求决定是否加入，通常读取状态不受enable/disable影响
    return gpio_get_level((gpio_num_t)s_pir_rel_pin);
}

esp_err_t s18l262_set_ontime(uint16_t seconds)
{
    if (!s_is_initialized) {
        ESP_LOGE(TAG, "驱动未初始化，无法设置延时时间。");
        return ESP_ERR_INVALID_STATE;
    }
    if (!s_is_sensor_enabled && s_use_pwm) { // 在GPIO模式下，引脚固定，此检查意义不大
        ESP_LOGW(TAG, "传感器未通过 s18l262_enable() 启用，设置延时时间可能无效 (PWM模式)。");
    }

    // 限制范围 (使用头文件中定义的范围)
    if (seconds < S18L262_MIN_ONTIME) {
        seconds = S18L262_MIN_ONTIME;
        ESP_LOGW(TAG, "延时时间低于最小值，已设置为 %" PRIu16 "s", seconds);
    } else if (seconds > S18L262_MAX_ONTIME) {
        seconds = S18L262_MAX_ONTIME;
        ESP_LOGW(TAG, "延时时间高于最大值，已设置为 %" PRIu16 "s", seconds);
    }

    ESP_LOGI(TAG, "设置延时时间: %" PRIu16 " 秒", seconds);

    if (s_use_pwm) {
        // 根据PDF P6，ONTIME电压范围 0V (对应 MIN_ONTIME) 至 VDD/2 (对应 MAX_ONTIME)
        // PWM占空比范围 0 至 ( (1 << PWM_RESOLUTION) / 2 )
        uint32_t max_duty_for_half_vdd = (1 << S18L262_PWM_RESOLUTION) / 2;
        uint32_t duty = 0;

        if (S18L262_MAX_ONTIME > S18L262_MIN_ONTIME) {
            duty = (uint32_t)(((float)(seconds - S18L262_MIN_ONTIME) / (S18L262_MAX_ONTIME - S18L262_MIN_ONTIME)) * max_duty_for_half_vdd);
        } else if (seconds == S18L262_MIN_ONTIME) { // 处理 MIN_ONTIME == MAX_ONTIME 的情况
            duty = 0; // 对应最小时间
        } else { // 如果 MIN_ONTIME == MAX_ONTIME 且 seconds > MIN_ONTIME (已被钳位到MAX_ONTIME)
            duty = max_duty_for_half_vdd; // 对应最大时间
        }


        // 确保占空比不超过 VDD/2 对应的最大值
        if (duty > max_duty_for_half_vdd) {
            duty = max_duty_for_half_vdd;
        }
         if (seconds == S18L262_MIN_ONTIME) duty = 0; // 确保最小值对应0占空比

        ESP_LOGD(TAG, "ONTIME PWM duty: %" PRIu32, duty);
        esp_err_t ret_duty = ledc_set_duty(LEDC_LOW_SPEED_MODE, S18L262_ONTIME_CHANNEL, duty);
        if (ret_duty != ESP_OK) {
            ESP_LOGE(TAG, "LEDC设置ONTIME占空比失败: %s", esp_err_to_name(ret_duty));
            return ret_duty;
        }
        esp_err_t ret_update = ledc_update_duty(LEDC_LOW_SPEED_MODE, S18L262_ONTIME_CHANNEL);
        if (ret_update != ESP_OK) {
            ESP_LOGE(TAG, "LEDC更新ONTIME占空比失败: %s", esp_err_to_name(ret_update));
            return ret_update;
        }
        return ESP_OK;
    } else {
        // 当前版本GPIO模式下，引脚已在init中拉高，此函数无额外操作或返回不支持
        ESP_LOGI(TAG, "GPIO模式：ONTIME引脚已固定拉低。");
        return ESP_OK; // 或者 ESP_ERR_NOT_SUPPORTED 如果希望明确表示不可调
    }
}

esp_err_t s18l262_set_sensitivity(uint8_t sensitivity_percent)
{
    if (!s_is_initialized) {
        ESP_LOGE(TAG, "驱动未初始化，无法设置灵敏度。");
        return ESP_ERR_INVALID_STATE;
    }
     if (!s_is_sensor_enabled && s_use_pwm) {
        ESP_LOGW(TAG, "传感器未通过 s18l262_enable() 启用，设置灵敏度可能无效 (PWM模式)。");
    }

    // 限制范围
    if (sensitivity_percent > S18L262_MAX_SENS) { // MIN_SENS 通常为0，所以只检查MAX
        sensitivity_percent = S18L262_MAX_SENS;
        ESP_LOGW(TAG, "灵敏度高于最大值，已设置为 %u%%", sensitivity_percent);
    }
    // uint8_t 不会小于0，所以 S18L262_MIN_SENS 的检查通常不需要，除非它被定义为非0

    ESP_LOGI(TAG, "设置灵敏度: %u%%", sensitivity_percent);

    if (s_use_pwm) {
        // 根据PDF P7，SENS电压范围：0V (对应 MAX_SENS, 100%) 至 VDD/2 (对应 MIN_SENS, 0%)
        // PWM占空比：0 (对应0V) 至 ( (1 << PWM_RESOLUTION) / 2 ) (对应VDD/2)
        // 灵敏度越高，电压越低，占空比越低。
        uint32_t max_duty_for_half_vdd = (1 << S18L262_PWM_RESOLUTION) / 2;
        uint32_t duty = 0;

        if (S18L262_MAX_SENS > S18L262_MIN_SENS) {
             // (100% - sens%) / (100% - 0%) * max_duty_for_half_vdd
            duty = (uint32_t)(((float)(S18L262_MAX_SENS - sensitivity_percent) / (S18L262_MAX_SENS - S18L262_MIN_SENS)) * max_duty_for_half_vdd);
        } else if (sensitivity_percent == S18L262_MAX_SENS) { // MIN_SENS == MAX_SENS
             duty = 0; // 最高灵敏度
        } else {
             duty = max_duty_for_half_vdd; // 最低灵敏度
        }


        if (duty > max_duty_for_half_vdd) {
            duty = max_duty_for_half_vdd;
        }
        if (sensitivity_percent == S18L262_MAX_SENS) duty = 0; // 确保最高灵敏度是0占空比

        ESP_LOGD(TAG, "SENS PWM duty: %" PRIu32, duty);
        esp_err_t ret_duty = ledc_set_duty(LEDC_LOW_SPEED_MODE, S18L262_SENS_CHANNEL, duty);
        if (ret_duty != ESP_OK) {
            ESP_LOGE(TAG, "LEDC设置SENS占空比失败: %s", esp_err_to_name(ret_duty));
            return ret_duty;
        }
        esp_err_t ret_update = ledc_update_duty(LEDC_LOW_SPEED_MODE, S18L262_SENS_CHANNEL);
         if (ret_update != ESP_OK) {
            ESP_LOGE(TAG, "LEDC更新SENS占空比失败: %s", esp_err_to_name(ret_update));
            return ret_update;
        }
        return ESP_OK;
    } else {
        ESP_LOGI(TAG, "GPIO模式：SENS引脚已低。");
        return ESP_OK; // 或者 ESP_ERR_NOT_SUPPORTED
    }
}

esp_err_t s18l262_enable(void)
{
    if (!s_is_initialized) {
        ESP_LOGE(TAG, "驱动未初始化，无法启用。");
        return ESP_ERR_INVALID_STATE;
    }
    if (s_is_sensor_enabled) {
        ESP_LOGI(TAG, "传感器已启用。");
        return ESP_OK;
    }

    // 如果配置了中断，启用GPIO中断
    if (s_intr_type != S18L262_INTR_NONE) {
        esp_err_t ret = gpio_intr_enable((gpio_num_t)s_pir_rel_pin);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "启用PIR_REL引脚 %d 中断失败: %s", s_pir_rel_pin, esp_err_to_name(ret));
            return ret;
        }
        ESP_LOGI(TAG, "PIR_REL引脚 %d 中断已启用。", s_pir_rel_pin);
    }

    s_is_sensor_enabled = true;
    ESP_LOGI(TAG, "S18L262传感器功能已启用。");
    return ESP_OK;
}

esp_err_t s18l262_disable(void)
{
    if (!s_is_initialized) {
        ESP_LOGE(TAG, "驱动未初始化，无法禁用。");
        return ESP_ERR_INVALID_STATE;
    }
    if (!s_is_sensor_enabled) {
        ESP_LOGI(TAG, "传感器已禁用。");
        return ESP_OK;
    }

    // 如果配置了中断，禁用GPIO中断
    if (s_intr_type != S18L262_INTR_NONE) {
        esp_err_t ret = gpio_intr_disable((gpio_num_t)s_pir_rel_pin);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "禁用PIR_REL引脚 %d 中断失败: %s", s_pir_rel_pin, esp_err_to_name(ret));
            return ret;
        }
        ESP_LOGI(TAG, "PIR_REL引脚 %d 中断已禁用。", s_pir_rel_pin);
    }

    s_is_sensor_enabled = false;
    ESP_LOGI(TAG, "S18L262传感器功能已禁用。");
    return ESP_OK;
}

esp_err_t s18l262_deinit(void) {
    if (!s_is_initialized) {
        ESP_LOGW(TAG, "驱动未初始化，无需反初始化。");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "开始反初始化S18L262驱动...");

    // 禁用传感器功能（会禁用中断）
    s18l262_disable();

    // 移除中断处理函数和可选地卸载服务
    if (s_intr_type != S18L262_INTR_NONE) {
        esp_err_t ret_remove = gpio_isr_handler_remove((gpio_num_t)s_pir_rel_pin);
        if (ret_remove != ESP_OK) {
            ESP_LOGW(TAG, "移除PIR_REL引脚 %d 的ISR处理函数失败: %s (可能已移除或未添加)", s_pir_rel_pin, esp_err_to_name(ret_remove));
        } else {
            ESP_LOGI(TAG, "PIR_REL引脚 %d 的ISR处理函数已移除。", s_pir_rel_pin);
        }

        if (s_is_isr_service_installed_by_us) {
            gpio_uninstall_isr_service();
            ESP_LOGI(TAG, "GPIO中断服务已卸载。");
            s_is_isr_service_installed_by_us = false;
        }
    }

    // 如果使用PWM，停止LEDC通道并将GPIO重置
    // 注意：LEDC定时器通常是共享资源，除非确定此驱动独占，否则不应在此处释放定时器配置。
    // ledc_stop 函数可以将占空比设为0并停止PWM信号。
    if (s_use_pwm) {
        ledc_stop(LEDC_LOW_SPEED_MODE, S18L262_ONTIME_CHANNEL, 0);
        ledc_stop(LEDC_LOW_SPEED_MODE, S18L262_SENS_CHANNEL, 0);
        ESP_LOGI(TAG, "PWM通道 %d (ONTIME) 和 %d (SENS) 已停止。", S18L262_ONTIME_CHANNEL, S18L262_SENS_CHANNEL);
    }

    // 重置GPIO引脚到默认状态 (高阻态)
    // 对于配置为LEDC输出的引脚，gpio_reset_pin会解除LEDC外设对引脚的控制。
    if (s_pir_rel_pin >= 0) gpio_reset_pin((gpio_num_t)s_pir_rel_pin);
    if (s_dac_set_ontime_pin >= 0) gpio_reset_pin((gpio_num_t)s_dac_set_ontime_pin);
    if (s_dac_set_sens_pin >= 0) gpio_reset_pin((gpio_num_t)s_dac_set_sens_pin);
    ESP_LOGI(TAG, "GPIO引脚 (%d, %d, %d) 已重置。", s_pir_rel_pin, s_dac_set_ontime_pin, s_dac_set_sens_pin);

    // 重置静态变量
    s_pir_rel_pin = -1;
    s_dac_set_ontime_pin = -1;
    s_dac_set_sens_pin = -1;
    s_use_pwm = false;
    s_intr_type = S18L262_INTR_NONE;
    s_intr_handler = NULL;
    s_intr_handler_arg = NULL;
    s_is_sensor_enabled = false;
    s_is_initialized = false;

    ESP_LOGI(TAG, "S18L262驱动反初始化完成。");
    return ESP_OK;
}
