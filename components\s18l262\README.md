# S18-L262B-2 人体感应传感器驱动

这是一个用于ESP32S3的S18-L262B-2人体感应传感器驱动。该驱动支持使用中断方式检测人体存在，并可以通过PWM模拟DAC来控制传感器的延时时间和灵敏度。

## 功能特点

- 支持中断方式检测人体存在
- 支持设置延时时间（保持输出的时间）
- 支持设置灵敏度
- 兼容未来使用PWM+RC滤波器实现DAC功能的硬件版本

## 硬件连接

默认引脚配置：
- PIR_REL: GPIO 39 - 人体感应输出引脚
- DAC_SET_ONTIME: GPIO 38 - 延时时间设置引脚
- DAC_SET_SENS: GPIO 46 - 灵敏度设置引脚

## 使用方法

### 1. 初始化传感器

```c
// 配置传感器
s18l262_config_t config = {
    .pir_rel_pin = 39,         // 人体感应输出引脚
    .dac_set_ontime_pin = 38,  // 延时时间设置引脚
    .dac_set_sens_pin = 46,    // 灵敏度设置引脚
    .use_pwm = false,          // 当前版本不使用PWM
    .intr_type = S18L262_INTR_ANY  // 任意边沿触发中断
};

// 初始化传感器
esp_err_t ret = s18l262_init(&config);
if (ret != ESP_OK) {
    ESP_LOGE(TAG, "初始化传感器失败: %d", ret);
    return;
}
```

### 2. 设置中断处理函数（如果使用中断）

```c
// 创建中断事件队列
QueueHandle_t pir_evt_queue = xQueueCreate(10, sizeof(uint32_t));

// 中断处理函数
void pir_intr_handler(void* arg)
{
    // 发送事件到队列
    uint32_t gpio_num = (uint32_t)arg;
    xQueueSendFromISR(pir_evt_queue, &gpio_num, NULL);
}

// 设置中断处理函数
ret = s18l262_set_intr_handler(pir_intr_handler, (void*)39);
if (ret != ESP_OK) {
    ESP_LOGE(TAG, "设置中断处理函数失败: %d", ret);
    return;
}

// 创建中断处理任务
xTaskCreate(pir_task, "pir_task", 2048, NULL, 10, NULL);
```

### 3. 设置延时时间和灵敏度

```c
// 设置延时时间为30秒
ret = s18l262_set_ontime(30);
if (ret != ESP_OK) {
    ESP_LOGE(TAG, "设置延时时间失败: %d", ret);
}

// 设置灵敏度为80%
ret = s18l262_set_sensitivity(80);
if (ret != ESP_OK) {
    ESP_LOGE(TAG, "设置灵敏度失败: %d", ret);
}
```

### 4. 读取传感器状态

```c
// 读取传感器状态
bool state = s18l262_read_state();
ESP_LOGI(TAG, "传感器状态: %s", state ? "检测到人体" : "未检测到人体");
```

## 未来硬件版本支持

当前版本的硬件没有RC滤波电路，因此DAC_SET_ONTIME和DAC_SET_SENS引脚只能设置为高电平。未来的硬件版本将使用PWM+RC滤波器实现DAC功能，可以通过设置`use_pwm = true`来启用PWM模式。

```c
// 配置传感器（未来硬件版本）
s18l262_config_t config = {
    .pir_rel_pin = 39,
    .dac_set_ontime_pin = 38,
    .dac_set_sens_pin = 46,
    .use_pwm = true,  // 启用PWM模式
    .intr_type = S18L262_INTR_ANY
};
```

## API参考

### 初始化和配置

- `esp_err_t s18l262_init(const s18l262_config_t* config)` - 初始化传感器
- `esp_err_t s18l262_set_intr_handler(s18l262_intr_handler_t handler, void* arg)` - 设置中断处理函数

### 状态控制

- `bool s18l262_read_state(void)` - 读取传感器状态
- `esp_err_t s18l262_enable(void)` - 启用传感器
- `esp_err_t s18l262_disable(void)` - 禁用传感器

### 参数设置

- `esp_err_t s18l262_set_ontime(uint16_t seconds)` - 设置延时时间
- `esp_err_t s18l262_set_sensitivity(uint8_t sensitivity)` - 设置灵敏度
