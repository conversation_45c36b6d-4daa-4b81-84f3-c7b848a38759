/*
 * bsp_esp32_nvs_flash.h
 *
 *  Created on: 2023年9月5日
 *      Author: KB402
 */
#include "bsp_esp32_nvs_flash.h"

#define RADAR_NVS_NAMESPACE      "radar_reg"

static const char *TAG = "bsp_esp32_nvs_flash.c";



int bsp_nvs_flash_init(void){
    esp_err_t err = nvs_flash_init();
    if (err == ESP_ERR_NVS_NO_FREE_PAGES || err == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        // NVS partition was truncated and needs to be erased
        // Retry nvs_flash_init
        ESP_ERROR_CHECK(nvs_flash_erase());
        err = nvs_flash_init();
    }
    ESP_ERROR_CHECK(err);
    return err;
}

int bsp_nvs_flash_write(char *key, char *value, uint16_t val_len)
{
    nvs_handle_t radar_nvs_handle;
    esp_err_t err;

    err = nvs_open(RADAR_NVS_NAMESPACE, NVS_READWRITE, &radar_nvs_handle);
	if (err != ESP_OK) {
		ESP_LOGE(TAG, "NVS open operation failed !!");
		return -1;
	}

	err = nvs_set_blob(radar_nvs_handle, key, (void *)value, val_len);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "NVS write operation failed !!");
        goto error;
    }

    /* NVS commit and close */
    err = nvs_commit(radar_nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "NVS commit operation failed !!");
        goto error;
    }

    nvs_close(radar_nvs_handle);
    return 0;

    error:
        nvs_close(radar_nvs_handle);
        return -1;
}

int bsp_nvs_flash_read(char *key, char *value, uint16_t val_len)
{
	nvs_handle_t radar_nvs_handle;
    esp_err_t err;
    size_t required_size = val_len;


    err = nvs_open(RADAR_NVS_NAMESPACE, NVS_READWRITE, &radar_nvs_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "NVS open operation failed");
        return -1;
    }

    err = nvs_get_blob(radar_nvs_handle, key, (void *)value, &required_size);

//    ef_get_env_blob(key, (void *)value, val_len, &required_size);

    if (required_size < val_len)
    {
    	ESP_LOGE(TAG, "[NV] get fail:%d .", required_size);
        return -1;
    }
    nvs_close(radar_nvs_handle);
    return 0;
}
