/*
 * fsl_esp_uart.h
 *
 *  Created on: 2023年9月5日
 *      Author: KB402
 */

#ifndef PLATFORM_H_
#define PLATFORM_H_

#include <string.h>

#include "led.h"
#include "esp_system.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_spi_flash.h"
#include "bsp_esp32_uart.h"
#include "bsp_esp32_iic.h"
#include "bsp_esp32_spi.h"
#include "bsp_esp32_nvs_flash.h"

#define INDICATOR_RECV_THRESHOLD            (1000)
#define INDICATOR_RECV_THD_DFFT_SHIFT       (3)
#define INDICATOR_RECV_THD_DPEAK_SHIFT      (6)
#define INDICATOR_SEND_OF_THRESHOLD         (10)


void Indicator_RadarDataReceived(uint16_t threshold);
void Indicator_RadarDataIndexError(void);
void Indicator_RadarDataRecvOverFlow(void);
void Indicator_RadarDataSendOverFlow(void);
void Indicator_CmdDataRecvOverFlow(void);

void Delay(uint32_t time);
void Platform_Init(void);
void radar_enable_init(void);
void bsp_boot_info(void);
void radar_reset(void);
#endif /* PLATFORM_H_ */
