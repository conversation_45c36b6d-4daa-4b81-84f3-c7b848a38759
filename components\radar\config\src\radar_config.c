/**
  ******************************************************************************
  * @file           : radar_config.c
  * <AUTHOR> <EMAIL>
  * @brief          : config module
  ******************************************************************************
  */
#include <stdio.h>
#include <string.h>
#include "radar_config.h"
#include "utilities.h"
#include "banyan.h"
#include "system.h"

static const char *TAG = "radar_config.c";
static FLASH_DATA_T flashData;
static uint8_t needFlashWrite = 0;

// FLASH_ELEM_MAX = 4
elem_key_t elem_key =
{
    .magic = "magic",
    .elem_len[0] = "elemLen1",
    .elem_len[1] = "elemLen2",
    .elem_len[2] = "elemLen3",
    .elem_len[3] = "elemLen4",
    .elem_member[0] = "elem_mb1",
    .elem_member[1] = "elem_mb2",
    .elem_member[2] = "elem_mb3",
    .elem_member[3] = "elem_mb4",
    .bodysensing = "bodyss",
    .parameter = "param",
};

/********************************************
 @名称；Config_ReloadAlgoRegister
 @功能：重新赋值参数
 @参数：systemMode - 系统模式
 @返回：none
*********************************************/
void Config_ReloadAlgoRegister(uint16_t systemMode)
{
#ifdef SUPPORT_DYNAMIC_SYS_MODE
    uint8_t reload = 1;
    uint16_t loop = 0;
    uint16_t readNum = 0;
    uint32_t *readAddr = NULL;
    int ret;

    // 根据系统模式决定是否需要重新加载参数
    switch(systemMode)
    {
        case SYS_MODE_MTT:
            readNum = Radar_GetChipRegListConfigParaLen(0) / sizeof(uint32_t);
            reload = 1;
            break;
        case SYS_MODE_VS:
            readNum = Radar_GetChipRegListConfigParaLen(0) / sizeof(uint32_t);
            reload = 1;
            break;
        case SYS_MODE_GR:
            readNum = Radar_GetChipRegListConfigParaLen(0) / sizeof(uint32_t);
            reload = 1;
            break;
        default:
            reload = 0;
            break;
    }

    if(reload)
    {
        // 检查参数长度是否合法
        if(readNum == 0)
        {
            ESP_LOGE(TAG, "配置参数长度不足，无法加载算法参数");
            return;
        }

        // 使用与Config_RetrieveFlashData相同的方式访问数据
        // 配置参数存储在FLASH_ELEM_CHIP_CONFIG_REG0中
        readAddr = (uint32_t *)Radar_GetChipRegListConfigParaAddr(0);
        if (readAddr == NULL) {
            ESP_LOGE(TAG, "配置参数地址无效");
            return;
        }

        // 从flashData中读取配置参数
        if (flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemAddr != NULL &&
            flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen > 0) {

            // 复制配置参数
            for (loop = 0; loop < (readNum - MAX_REG_FIX_NUM); loop++) {
                if (loop * sizeof(uint32_t) < flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen) {
                    readAddr[loop] = ((uint32_t *)flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemAddr)[loop];
                } else {
                    readAddr[loop] = 0;
                }
            }
            readAddr[loop] = 0x00000000; // 确保数据以0结尾

            ESP_LOGI(TAG, "成功加载配置参数，大小: %d 字节", (readNum - MAX_REG_FIX_NUM) * sizeof(uint32_t));
        } else {
            ESP_LOGW(TAG, "配置参数不存在或无效，使用默认值");
            // 设置默认值
            for (loop = 0; loop < readNum - MAX_REG_FIX_NUM; loop++) {
                readAddr[loop] = 0;
            }
            readAddr[loop] = 0x00000000;
        }

        // 启动参数存储在FLASH_ELEM_CHIP_START_REG0中
        readAddr = (uint32_t *)Radar_GetChipRegListStartParaAddr(0);
        if (readAddr == NULL) {
            ESP_LOGE(TAG, "启动参数地址无效");
            return;
        }

        // 从flashData中读取启动参数
        if (flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemAddr != NULL &&
            flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen > 0) {

            // 复制启动参数
            for (loop = 0; loop < REG_FIX_NUM; loop++) {
                if (loop * sizeof(uint32_t) < flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen) {
                    readAddr[loop] = ((uint32_t *)flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemAddr)[loop];
                } else {
                    readAddr[loop] = 0;
                }
            }
            readAddr[loop] = 0x00000000; // 确保数据以0结尾

            ESP_LOGI(TAG, "成功加载启动参数，大小: %d 字节", REG_FIX_NUM * sizeof(uint32_t));
        } else {
            ESP_LOGW(TAG, "启动参数不存在或无效，使用默认值");
            // 设置默认值
            for (loop = 0; loop < REG_FIX_NUM; loop++) {
                readAddr[loop] = 0;
            }
            readAddr[loop] = 0x00000000;
        }
    }
#endif
}

/********************************************
 @名称；Config_WriteData2Flash
 @功能：将重新配置的flashData写入flash
 @参数：none
 @返回：none
*********************************************/
void Config_WriteData2Flash(void)
{
    uint16_t idx = 0;
    int err;

    flashData.magic = FLASH_MAGIC_NUM;

    err = bsp_nvs_flash_write(elem_key.magic, (char *)&flashData.magic, sizeof(flashData.magic));
    if (err != ESP_OK)
    {
    	ESP_LOGE(TAG, "save magic failed\r\n");
    }

    for (idx = 0; idx < FLASH_ELEM_MAX; idx++)
    {
    	bsp_nvs_flash_write(elem_key.elem_len[idx], (char *)&flashData.elem[idx].elemLen, sizeof(flashData.elem[idx].elemLen));
    	bsp_nvs_flash_write(elem_key.elem_member[idx], (char *)flashData.elem[idx].elemAddr, flashData.elem[idx].elemLen/FLASH_WORD_LEN);
    }
}

/********************************************
 @名称；Config_RetrieveFlashData
 @功能：Retrieve Flash Data
 @参数：none
 @返回：none
*********************************************/
void Config_RetrieveFlashData(void)
{
    uint16_t idx = 0;

    bsp_nvs_flash_read(elem_key.magic, (char *)&flashData.magic, sizeof(flashData.magic));
    ESP_LOGI(TAG, "flashData.magic=%#x", flashData.magic);
    if (FLASH_MAGIC_NUM != flashData.magic)
    {
    	ESP_LOGI(TAG, "flash setting is empty!");
        Config_WriteData2Flash();               // 往flash写入数据
    }

    flashData.magic = 0;
    bsp_nvs_flash_read(elem_key.magic, (char *)&flashData.magic, sizeof(flashData.magic));
    if (FLASH_MAGIC_NUM != flashData.magic) // 写入失败擦除参数，死循环
    {
    	ESP_LOGE(TAG, "Error: flash work abnormal!");
    }

    for (idx = 0; idx < FLASH_ELEM_MAX; idx++)
    {
    	bsp_nvs_flash_read(elem_key.elem_len[idx], (char *)&flashData.elem[idx].elemLen, sizeof(flashData.elem[idx].elemLen));
    	ESP_LOGI(TAG, "flashData.elem[%d].elemLen=%d", idx, flashData.elem[idx].elemLen);
    	ESP_LOGI(TAG, "flashData.elem[%d].elemAddr=%p", idx, flashData.elem[idx].elemAddr);
        bsp_nvs_flash_read(elem_key.elem_member[idx], (char *)flashData.elem[idx].elemAddr, flashData.elem[idx].elemLen/FLASH_WORD_LEN);
    }
	Config_ReloadAlgoRegister(System_GetSysMode());
}

/********************************************
 @名称；Config_SavePara2Flash
 @功能：参数写入flash的入口函数
 @参数：none
 @返回：none
*********************************************/
void Config_SavePara2Flash(void)
{
    if (!needFlashWrite)
    {
        return;
    }

    Config_WriteData2Flash();
    needFlashWrite = 0;
}

/********************************************
 @名称；Config_EarseFlashData
 @功能：擦除flash页
 @参数：none
 @返回：none
*********************************************/
void Config_EarseFlashData(void)
{
    int ret;
    uint16_t idx;

    // 擦除magic标记
    ret = bsp_nvs_flash_write(elem_key.magic, (char *)&idx, 0); // 写入0长度数据相当于删除键
    if (ret != 0) {
        ESP_LOGE(TAG, "擦除magic标记失败");
    }

    // 擦除所有元素的长度和数据
    for (idx = 0; idx < FLASH_ELEM_MAX; idx++) {
        ret = bsp_nvs_flash_write(elem_key.elem_len[idx], (char *)&idx, 0);
        if (ret != 0) {
            ESP_LOGE(TAG, "擦除元素长度[%d]失败", idx);
        }

        ret = bsp_nvs_flash_write(elem_key.elem_member[idx], (char *)&idx, 0);
        if (ret != 0) {
            ESP_LOGE(TAG, "擦除元素数据[%d]失败", idx);
        }
    }

    // 擦除bodysensing相关数据
    ret = bsp_nvs_flash_write(elem_key.bodysensing, (char *)&idx, 0);
    if (ret != 0) {
        ESP_LOGE(TAG, "擦除bodysensing长度失败");
    }

    ret = bsp_nvs_flash_write(elem_key.parameter, (char *)&idx, 0);
    if (ret != 0) {
        ESP_LOGE(TAG, "擦除parameter数据失败");
    }

    ESP_LOGI(TAG, "NVS数据擦除完成");
}

/********************************************
 @名称；Config_NeedFlashWrite
 @功能：置位flash写入flag
 @参数：none
 @返回：none
*********************************************/
void Config_NeedFlashWrite(void)
{
    needFlashWrite = 1;
}

/********************************************
 @名称；Config_SaveAlgoRegister
 @功能：保存算法参数到NVS
 @参数：systemMode - 系统模式
 @返回：esp_err_t - 操作结果
*********************************************/
esp_err_t Config_SaveAlgoRegister(uint16_t systemMode)
{
#ifdef SUPPORT_DYNAMIC_SYS_MODE
    int ret;
    uint32_t *configAddr = NULL;
    uint32_t *startAddr = NULL;
    uint32_t configLen = 0;
    uint32_t startLen = 0;

    // 获取配置参数和启动参数的地址和长度
    configAddr = (uint32_t *)Radar_GetChipRegListConfigParaAddr(0);
    configLen = Radar_GetChipRegListConfigParaLen(0);

    startAddr = (uint32_t *)Radar_GetChipRegListStartParaAddr(0);
    startLen = Radar_GetChipRegListStartParaLen(0);

    if (configAddr == NULL || startAddr == NULL || configLen == 0 || startLen == 0) {
        ESP_LOGE(TAG, "无效的参数地址或长度");
        return ESP_ERR_INVALID_ARG;
    }

    // 使用与Config_WriteData2Flash相同的方式保存数据
    // 更新flashData结构体中的数据
    flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemAddr = configAddr;
    flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen = configLen;

    flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemAddr = startAddr;
    flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen = startLen;

    // 设置magic标记
    flashData.magic = FLASH_MAGIC_NUM;

    // 保存magic标记
    ret = bsp_nvs_flash_write(elem_key.magic, (char *)&flashData.magic, sizeof(flashData.magic));
    if (ret != 0) {
        ESP_LOGE(TAG, "保存magic标记失败");
        return ESP_FAIL;
    }

    // 保存配置参数
    ret = bsp_nvs_flash_write(elem_key.elem_len[FLASH_ELEM_CHIP_CONFIG_REG0],
                             (char *)&flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen,
                             sizeof(flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen));
    if (ret != 0) {
        ESP_LOGE(TAG, "保存配置参数长度失败");
        return ESP_FAIL;
    }

    ret = bsp_nvs_flash_write(elem_key.elem_member[FLASH_ELEM_CHIP_CONFIG_REG0],
                             (char *)flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemAddr,
                             flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen);
    if (ret != 0) {
        ESP_LOGE(TAG, "保存配置参数数据失败");
        return ESP_FAIL;
    }

    // 保存启动参数
    ret = bsp_nvs_flash_write(elem_key.elem_len[FLASH_ELEM_CHIP_START_REG0],
                             (char *)&flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen,
                             sizeof(flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen));
    if (ret != 0) {
        ESP_LOGE(TAG, "保存启动参数长度失败");
        return ESP_FAIL;
    }

    ret = bsp_nvs_flash_write(elem_key.elem_member[FLASH_ELEM_CHIP_START_REG0],
                             (char *)flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemAddr,
                             flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen);
    if (ret != 0) {
        ESP_LOGE(TAG, "保存启动参数数据失败");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "成功保存算法参数到NVS");
    return ESP_OK;
#else
    return ESP_OK;
#endif
}

/********************************************
 @名称；Config_GetSN
 @功能：回读SN
 @参数：none
 @返回：SN号地址
*********************************************/
//uint32_t Config_GetSN(void)
//{
//    uint32_t flashAddr = FLASH_SN_ADDR;

//    return REG32(flashAddr);
//}

/********************************************
 @名称；Config_Init
 @功能：参数配置初始化
 @参数：none
 @返回：none
*********************************************/
void Config_Init(void)
{
    uint32_t totalLen = 0;

    // 计算配置数据的总长度
    totalLen += sizeof(flashData.magic);

    flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemAddr = Radar_GetChipRegListConfigParaAddr(0);
    flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen = Radar_GetChipRegListConfigParaLen(0);
    totalLen += flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen + sizeof(flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen);

    flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemAddr = Radar_GetChipRegListStartParaAddr(0);
    flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen = Radar_GetChipRegListStartParaLen(0);
    totalLen += flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen + sizeof(flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen);

    ESP_LOGI(TAG, "flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemAddr=%p", flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemAddr);
    ESP_LOGI(TAG, "flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen=%d", flashData.elem[FLASH_ELEM_CHIP_CONFIG_REG0].elemLen);
    ESP_LOGI(TAG, "flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemAddr=%p", flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemAddr);
    ESP_LOGI(TAG, "flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen=%d", flashData.elem[FLASH_ELEM_CHIP_START_REG0].elemLen);

    // 初始化系统参数并获取地址和大小，然后添加到总长度中
    System_ParaInit();
    flashData.elem[FLASH_ELEM_SYS].elemAddr = System_GetSysParaAddr();
    flashData.elem[FLASH_ELEM_SYS].elemLen = System_GetSysParaLen();
    totalLen += flashData.elem[FLASH_ELEM_SYS].elemLen + sizeof(flashData.elem[FLASH_ELEM_SYS].elemLen);

    uint8_t defaultSn[8] = "12345678";
    System_SetSysSnInfo(8, defaultSn);
    flashData.elem[FLASH_ELEM_SN].elemAddr = System_GetSysSnInfoAddr();
    flashData.elem[FLASH_ELEM_SN].elemLen = System_GetSysSnInfoLen();
    totalLen += flashData.elem[FLASH_ELEM_SN].elemLen + sizeof(flashData.elem[FLASH_ELEM_SN].elemLen);

    ESP_LOGI(TAG, "flashData.elem[FLASH_ELEM_SYS].elemAddr=%p", flashData.elem[FLASH_ELEM_SYS].elemAddr);
    ESP_LOGI(TAG, "flashData.elem[FLASH_ELEM_SYS].elemLen=%d", flashData.elem[FLASH_ELEM_SYS].elemLen);

    // 检查总长度是否超过闪存页面大小
    if (totalLen > FLASH_PAGE_SIZE) // 1K
    {
    	ESP_LOGE(TAG, "Error: flashDataLen is more than FLASH_PAGE_SIZE!");
        RunFailed((uint8_t *)__FILE__, __LINE__);
    }

    Config_RetrieveFlashData(); // flash有参数则读取参数，无参数则写入参数
    //printf("sn: 0x%x\r\n", Config_GetSN());
}



void Config_WritebodysensingFlash(uint32_t* addr, uint16_t len)
{
    uint32_t dataLen = len;

    bsp_nvs_flash_write(elem_key.bodysensing, (char *)&dataLen, sizeof(dataLen));
    bsp_nvs_flash_write(elem_key.parameter, (char *)addr, len);
}

uint32_t Config_ReadAlgorithmParamLen(void)
{
    int ret;
    uint32_t dataLen = 0xffffffff;

    ret = bsp_nvs_flash_read(elem_key.bodysensing, (char *)&dataLen, sizeof(dataLen));
    if (ret != 0)
    {
        dataLen = 0xffffffff;
        ESP_LOGE(TAG,"read dataLen failed!");
    }

    return dataLen;
}

