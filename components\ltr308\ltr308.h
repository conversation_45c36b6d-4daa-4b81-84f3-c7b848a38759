#ifndef LTR308_H
#define LTR308_H

#include <stdint.h>
#include <stdbool.h>

// LTR308 寄存器地址
#define LTR308_REG_CONTR         0x00
#define LTR308_REG_MEAS_RATE     0x04
#define LTR308_REG_ALS_GAIN      0x05
#define LTR308_REG_PART_ID       0x06
#define LTR308_REG_STATUS        0x07
#define LTR308_REG_DATA_0        0x0D
#define LTR308_REG_DATA_1        0x0E
#define LTR308_REG_DATA_2        0x0F
#define LTR308_REG_INTERRUPT     0x19
#define LTR308_REG_INTR_PERS     0x1A
#define LTR308_REG_THRES_UP_0    0x21
#define LTR308_REG_THRES_UP_1    0x22
#define LTR308_REG_THRES_UP_2    0x23
#define LTR308_REG_THRES_LOW_0   0x24
#define LTR308_REG_THRES_LOW_1   0x25
#define LTR308_REG_THRES_LOW_2   0x26

#define LTR308_ADDR              0x53
#define LTR308_PART_V_ID         0xB1

// 增益枚举
typedef enum {
    LTR308_GAIN_1X = 0x00,
    LTR308_GAIN_3X,
    LTR308_GAIN_6X,
    LTR308_GAIN_9X,
    LTR308_GAIN_18X
} ltr308_gain_t;

// 中断持续性枚举
typedef enum {
    LTR308_INTR_TRIG_1 = 0x00,
    LTR308_INTR_TRIG_2,
    LTR308_INTR_TRIG_3,
    LTR308_INTR_TRIG_4,
    LTR308_INTR_TRIG_5,
    LTR308_INTR_TRIG_6,
    LTR308_INTR_TRIG_7,
    LTR308_INTR_TRIG_8,
    LTR308_INTR_TRIG_9,
    LTR308_INTR_TRIG_10,
    LTR308_INTR_TRIG_11,
    LTR308_INTR_TRIG_12,
    LTR308_INTR_TRIG_13,
    LTR308_INTR_TRIG_14,
    LTR308_INTR_TRIG_15,
    LTR308_INTR_TRIG_16
} ltr308_intr_persist_t;

// 分辨率枚举
typedef enum {
    LTR308_RES_400MS_20BIT = 0x00,
    LTR308_RES_200MS_19BIT,
    LTR308_RES_100MS_18BIT,
    LTR308_RES_50MS_17BIT,
    LTR308_RES_25MS_16BIT
} ltr308_resolution_t;

// 测量速率枚举
typedef enum {
    LTR308_RATE_25MS = 0x00,
    LTR308_RATE_50MS,
    LTR308_RATE_100MS,
    LTR308_RATE_500MS,
    LTR308_RATE_1000MS = 5,
    LTR308_RATE_2000MS,
    LTR308_RATE_2000MS_2
} ltr308_meas_rate_t;

// 测量速率和分辨率结构体
typedef struct {
    ltr308_resolution_t resolution;
    ltr308_meas_rate_t meas_rate;
} ltr308_meas_config_t;

// 传感器状态结构体
typedef struct {
    bool pon_status;
    bool intr_status;
    bool data_status;
} ltr308_status_t;

// 阈值结构体
typedef struct {
    uint32_t upper_limit;
    uint32_t lower_limit;
} ltr308_threshold_t;

// 初始化LTR308传感器
typedef struct {
    uint8_t i2c_addr;
    int i2c_port;
    int sda_pin;
    int scl_pin;
    int isr_pin;
} ltr308_config_t;

/************************************************************************
 @名称：ltr308_init
 @功能：初始化LTR308传感器
 @参数：config，传感器配置
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_init(const ltr308_config_t *config);

/************************************************************************
 @名称：ltr308_set_power_up
 @功能：设置传感器上电
 @参数：无
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_power_up(void);

/************************************************************************
 @名称：ltr308_set_power_down
 @功能：设置传感器断电
 @参数：无
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_power_down(void);

/************************************************************************
 @名称：ltr308_get_power
 @功能：获取传感器电源状态
 @参数：无
 @返回：电源控制寄存器值
*************************************************************************/
uint8_t ltr308_get_power(void);

/************************************************************************
 @名称：ltr308_set_gain
 @功能：设置传感器增益
 @参数：gain，增益值
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_gain(ltr308_gain_t gain);

/************************************************************************
 @名称：ltr308_get_gain
 @功能：获取传感器增益
 @参数：无
 @返回：增益值
*************************************************************************/
uint8_t ltr308_get_gain(void);

/************************************************************************
 @名称：ltr308_set_measurement_rate
 @功能：设置测量速率和分辨率
 @参数：meas_config，测量配置
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_measurement_rate(ltr308_meas_config_t meas_config);

/************************************************************************
 @名称：ltr308_set_measurement_rate_separate
 @功能：分别设置测量速率和分辨率
 @参数：resolution，分辨率；meas_rate，测量速率
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_measurement_rate_separate(ltr308_resolution_t resolution, ltr308_meas_rate_t meas_rate);

/************************************************************************
 @名称：ltr308_get_measurement_rate
 @功能：获取测量速率和分辨率
 @参数：meas_config，用于存储获取的测量配置
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_get_measurement_rate(ltr308_meas_config_t *meas_config);

/************************************************************************
 @名称：ltr308_get_part_id
 @功能：获取传感器ID
 @参数：无
 @返回：传感器ID
*************************************************************************/
uint8_t ltr308_get_part_id(void);

/************************************************************************
 @名称：ltr308_get_status
 @功能：获取传感器状态
 @参数：status，用于存储获取的状态
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_get_status(ltr308_status_t *status);

/************************************************************************
 @名称：ltr308_get_data
 @功能：获取传感器原始数据
 @参数：无
 @返回：传感器原始数据
*************************************************************************/
uint32_t ltr308_get_data(void);

/************************************************************************
 @名称：ltr308_set_interrupt_control
 @功能：设置中断控制
 @参数：mode，中断模式（true启用，false禁用）
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_interrupt_control(bool mode);

/************************************************************************
 @名称：ltr308_get_interrupt_control
 @功能：获取中断控制状态
 @参数：无
 @返回：中断控制状态（true启用，false禁用）
*************************************************************************/
bool ltr308_get_interrupt_control(void);

/************************************************************************
 @名称：ltr308_set_intr_persist
 @功能：设置中断持续性
 @参数：persist，中断持续性
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_intr_persist(ltr308_intr_persist_t persist);

/************************************************************************
 @名称：ltr308_get_intr_persist
 @功能：获取中断持续性
 @参数：无
 @返回：中断持续性值
*************************************************************************/
uint8_t ltr308_get_intr_persist(void);

/************************************************************************
 @名称：ltr308_set_threshold
 @功能：设置阈值
 @参数：threshold，阈值结构体
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_threshold(ltr308_threshold_t threshold);

/************************************************************************
 @名称：ltr308_set_threshold_separate
 @功能：分别设置上下阈值
 @参数：upper_limit，上限；lower_limit，下限
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_threshold_separate(uint32_t upper_limit, uint32_t lower_limit);

/************************************************************************
 @名称：ltr308_get_threshold
 @功能：获取阈值
 @参数：threshold，用于存储获取的阈值
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_get_threshold(ltr308_threshold_t *threshold);

/************************************************************************
 @名称：ltr308_get_lux
 @功能：获取光照强度（lux）
 @参数：gain，增益；resolution，分辨率；als_data，原始数据
 @返回：光照强度值
*************************************************************************/
double ltr308_get_lux(ltr308_gain_t gain, ltr308_resolution_t resolution, uint32_t als_data);

/************************************************************************
 @名称：ltr308_get_lux_simple
 @功能：使用当前配置获取光照强度（lux）
 @参数：als_data，原始数据
 @返回：光照强度值
*************************************************************************/
double ltr308_get_lux_simple(uint32_t als_data);

/************************************************************************
 @名称：ltr308_read_lux
 @功能：读取光照强度（lux）
 @参数：lux，用于存储光照强度值
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_read_lux(uint32_t *lux);

/************************************************************************
 @名称：ltr308_read_interrupt_pin
 @功能：读取中断引脚状态
 @参数：isr_pin，中断引脚号
 @返回：引脚状态（0或1）
*************************************************************************/
int ltr308_read_interrupt_pin(int isr_pin);

#endif // LTR308_H
