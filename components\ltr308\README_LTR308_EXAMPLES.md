# LTR308 光照传感器示例集

本项目包含多个使用 LTR308 光照传感器的示例程序，展示了如何在 ESP32 上使用 C 语言操作该传感器。

## 示例列表

1. **简单读取示例** (`ltr308_simple_example.c`)
   - 基本的传感器初始化和配置
   - 周期性读取光照强度数据
   - 显示传感器状态信息

2. **中断触发示例** (`ltr308_interrupt_example.c`)
   - 配置传感器的中断功能
   - 设置阈值上下限
   - 设置中断持续性（需要连续多少次超过阈值才触发中断）
   - 检测中断触发并处理

## 硬件连接

将 LTR308 传感器连接到 ESP32 的 I2C 接口：
- SDA: GPIO 8 (默认)
- SCL: GPIO 9 (默认)
- VCC: 3.3V
- GND: GND

## 如何切换示例

由于项目中包含多个示例程序，您需要选择要运行的示例。有两种方法可以实现这一点：

### 方法一：修改 CMakeLists.txt

1. 打开 `main/CMakeLists.txt` 文件
2. 将您想要运行的示例文件放在 SRCS 列表的第一位，例如：
   ```cmake
   idf_component_register(SRCS 
                       "ltr308_simple_example.c"    # 这个示例将被编译和运行
                       "ltr308_interrupt_example.c" # 这些示例不会被运行
                       "blink_example_main.c"       # 因为它们的 app_main 函数会被忽略
                       INCLUDE_DIRS ".")
   ```

### 方法二：使用条件编译

1. 在 `main/CMakeLists.txt` 中添加一个编译选项：
   ```cmake
   idf_component_register(SRCS 
                       "ltr308_simple_example.c"
                       "ltr308_interrupt_example.c"
                       "blink_example_main.c"
                       INCLUDE_DIRS ".")
   
   # 选择要运行的示例
   # 0: blink_example_main.c
   # 1: ltr308_simple_example.c
   # 2: ltr308_interrupt_example.c
   target_compile_definitions(${COMPONENT_LIB} PRIVATE EXAMPLE_SELECT=1)
   ```

2. 然后在每个示例文件中添加条件编译代码：
   ```c
   #if EXAMPLE_SELECT == 1
   void app_main(void)
   {
       // 示例代码...
   }
   #endif
   ```

## 编译和烧录

```bash
# 编译项目
idf.py build

# 烧录到 ESP32
idf.py flash

# 监视串口输出
idf.py monitor

# 或者一次性执行所有操作
idf.py build flash monitor
```

## 传感器配置选项

### 增益设置
- `LTR308_GAIN_1X`: 1倍增益
- `LTR308_GAIN_3X`: 3倍增益（传感器默认）
- `LTR308_GAIN_6X`: 6倍增益
- `LTR308_GAIN_9X`: 9倍增益
- `LTR308_GAIN_18X`: 18倍增益

### 分辨率设置
- `LTR308_RES_400MS_20BIT`: 20位结果，转换时间 = 400ms
- `LTR308_RES_200MS_19BIT`: 19位结果，转换时间 = 200ms
- `LTR308_RES_100MS_18BIT`: 18位结果，转换时间 = 100ms（传感器默认）
- `LTR308_RES_50MS_17BIT`: 17位结果，转换时间 = 50ms
- `LTR308_RES_25MS_16BIT`: 16位结果，转换时间 = 25ms

### 测量速率设置
- `LTR308_RATE_25MS` 到 `LTR308_RATE_2000MS_2`: 测量速率从 25ms 到 2000ms

## 参考资料

- [LTR308 数据手册](https://www.liteon.com/en-us/product/sensor)
- [ESP-IDF 编程指南](https://docs.espressif.com/projects/esp-idf/en/latest/esp32/index.html)
