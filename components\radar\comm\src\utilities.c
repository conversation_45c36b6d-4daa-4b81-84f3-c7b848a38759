/**
  ******************************************************************************
  * @file    utilities.c
  * <AUTHOR>
  * @brief   project utilities, tools
  ******************************************************************************
  */
#include "esp_log.h"
#include <stdio.h>
#include "utilities.h"
#include "led.h"
#include "radar_config.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

static const char *TAG = "utilities.c";

/************************************************************************
 @名称；RunFailed
 @功能：程序运行失败loop
 @参数：file，文件指针
        line，行数
 @返回：none
*************************************************************************/
void RunFailed(uint8_t *file, uint32_t line)
{
	ESP_LOGE(TAG, "Run failed: file %s line %d", file, line);


    led_on(LED_ERR);

    Config_EarseFlashData();

    while (1)
	{
        vTaskDelay(1000 / portTICK_PERIOD_MS);
	}
}


