#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "led.h"
#include <stdio.h>
#include "driver/gpio.h"

static const char *TAG ="led.c";

// LED引脚数组
static const uint8_t led_gpio_pins[3] = {BLINK_GPIO_COMM, BLINK_GPIO_CHIRP, BLINK_GPIO_ERR};

// 任务函数，通过led流动就是正常初始化中
static void init_status_task(void *arg) {
    volatile bool *p_finish = (volatile bool *)arg;

    // 添加指针检查
    if (p_finish == NULL) {
        ESP_LOGE(TAG, "初始化任务收到空指针，使用默认超时退出");
        // 如果指针为空，设置一个超时时间后自动退出
        int timeout_count = 50; // 10秒后退出(50 * 200ms)
        while(timeout_count > 0) {
            vTaskDelay(pdMS_TO_TICKS(200));
            led_cycle();
            timeout_count--;
        }
        ESP_LOGI(TAG, "初始化任务超时退出");
        vTaskDelete(NULL);
        return;
    }

    ESP_LOGI(TAG, "LED初始化任务启动，等待初始化完成标志");
    while(1) {
        vTaskDelay(pdMS_TO_TICKS(200)); // 等待200毫秒
        led_cycle();
        // 检查完成标志
        if (*p_finish) {
            ESP_LOGI(TAG, "板卡初始化完成!");
            led_off_all();
            break;
        }
    }
    vTaskDelete(NULL); // 删除自身任务
}

void led_init(bool *finish_flag) {
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << BLINK_GPIO_COMM) | (1ULL << BLINK_GPIO_CHIRP) | (1ULL << BLINK_GPIO_ERR),
        .pull_down_en = 0,
        .pull_up_en = 0
    };
    gpio_config(&io_conf);
    led_off_all();
    xTaskCreate(init_status_task, "init_status_task", 2048, (void*)finish_flag, 1, NULL);
}

void led_on(led_type_t led) {
    // 先熄灭所有LED
    for (int i = 0; i < 3; ++i) {
        gpio_set_level(led_gpio_pins[i], 1);
    }
    // 点亮指定LED
    if (led >= 0 && led < 3) {
        gpio_set_level(led_gpio_pins[led], 0);
    }
}

void led_off_all(void) {
    for (int i = 0; i < 3; ++i) {
        gpio_set_level(led_gpio_pins[i], 1);
    }
}

void led_toggle(led_type_t led) {
    // 为每个LED维护独立的状态计数器
    static uint32_t led_toggle_counters[3] = {0, 0, 0};

    // 检查LED类型是否有效
    if (led < 0 || led >= 3) {
        return;
    }

    // 对应LED的计数器加1
    led_toggle_counters[led]++;

    // 根据计数器的奇偶性决定LED状态
    if (led_toggle_counters[led] % 2 == 1) {
        // 奇数次调用：LED开启
        led_on(led);
    } else {
        // 偶数次调用：LED关闭
        led_off_all();
    }
}

void led_cycle(void) {
    static int idx = 0;
    led_on((led_type_t)idx);
    idx = (idx + 1) % 3;
}


