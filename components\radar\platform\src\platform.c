/*
 * platform.c
 *
 *  Created on: 2023年9月5日
 *      Author: KB402
 */

#include "platform.h"
#include "esp_clk.h" // 包含时钟相关函数

static const char *TAG = "platform.c";

#define RADAR_RST_PIN		18
#define RADAR_PWR_CTL       21 //低电平供电
#define RAW_CLK             17
#define RAW_READY           12
static void raw_gpio_input_init(void);
static void radar_pwr_ctl_init(void);
static void radar_rst_gpio_init(void);
// // 建议加上 volatile 关键字，防止编译器优化导致某些任务读取不到最新值。
// volatile bool init_finish = false;

void Delay(uint32_t time)
{
    vTaskDelay(pdMS_TO_TICKS(time));
}


void Indicator_RadarDataReceived(uint16_t threshold)
{
    static uint16_t recvCnt = 0;

    recvCnt++;
    if (recvCnt % threshold == 0)
    {
       led_toggle(LED_CHIRP);
    }
}

void Indicator_RadarDataIndexError(void)
{

    led_toggle(LED_ERR);
}

void Indicator_RadarDataRecvOverFlow(void)
{
    led_toggle(LED_COMM);
}

void Indicator_RadarDataSendOverFlow(void)
{
    static uint8_t recvCnt = 0;

    recvCnt++;
    if (recvCnt % INDICATOR_SEND_OF_THRESHOLD == 0)
    {
        led_toggle(LED_COMM);
    }
}

void Indicator_CmdDataRecvOverFlow(void)
{
    led_toggle(LED_COMM);
}


void Platform_Init(void){

	// 初始化串口
	bsp_uart0_init(115200);

	// 初始化 nvs_flash
	bsp_nvs_flash_init();

	//雷达复位
	radar_enable_init();

	// 初始化 IIC
	bsp_iic_master_init(I2C_Speed_Config_400k);
	vTaskDelay(10 / portTICK_PERIOD_MS);

	// 输出启动信息
	bsp_boot_info();
}

void radar_enable_init(void){

	raw_gpio_input_init();//设置雷达的24,25引脚
	radar_pwr_ctl_init();//设置雷达的供电引脚
	vTaskDelay(100 / portTICK_PERIOD_MS);
	gpio_set_level(RADAR_PWR_CTL, 0);//供电
	vTaskDelay(400 / portTICK_PERIOD_MS);
	radar_rst_gpio_init();
}

void radar_reset(void){
	gpio_set_level(RADAR_RST_PIN, 0);
	vTaskDelay(100 / portTICK_RATE_MS); // 等待1秒
	gpio_set_level(RADAR_RST_PIN, 1);
}

void bsp_boot_info(void){
	    // 打印系统信息
	    ESP_LOGI(TAG, "=== 系统启动信息 ===");

	    // 获取并打印CPU频率
	    uint32_t cpu_freq_mhz = esp_clk_cpu_freq() / 1000000;
	    ESP_LOGI(TAG, "CPU 运行频率: %u MHz", cpu_freq_mhz);

	    // 获取并打印APB频率（外设总线频率）
	    uint32_t apb_freq_mhz = esp_clk_apb_freq() / 1000000;
	    ESP_LOGI(TAG, "APB 总线频率: %u MHz", apb_freq_mhz);

	    // 获取并打印XTAL频率（晶振频率）
	    uint32_t xtal_freq_mhz = esp_clk_xtal_freq() / 1000000;
	    ESP_LOGI(TAG, "XTAL 晶振频率: %u MHz", xtal_freq_mhz);

	    ESP_LOGI(TAG, "==================");

	    /* Print chip information */
	    esp_chip_info_t chip_info;
	    esp_chip_info(&chip_info);
	    ESP_LOGI(TAG, "This is %s chip with %d CPU core(s), WiFi%s%s, ",
	            CONFIG_IDF_TARGET,
	            chip_info.cores,
	            (chip_info.features & CHIP_FEATURE_BT) ? "/BT" : "",
	            (chip_info.features & CHIP_FEATURE_BLE) ? "/BLE" : "");

	    ESP_LOGI(TAG, "silicon revision %d, ", chip_info.revision);
	    // 输出版本信息
	    ESP_LOGI(TAG, "ESP32 Version: %s", esp_get_idf_version());
	    ESP_LOGI(TAG, "%dMB %s flash", spi_flash_get_chip_size() / (1024 * 1024),
	            (chip_info.features & CHIP_FEATURE_EMB_FLASH) ? "embedded" : "external");

	    ESP_LOGI(TAG, "Minimum free heap size: %d bytes", esp_get_minimum_free_heap_size());

	    // 打印大字体FSL - 使用ESP_LOGI替代uart_write_bytes
	    ESP_LOGI(TAG, "##############################################");
	    ESP_LOGI(TAG, "##   ███████╗    ███████╗        ██╗        ##");
	    ESP_LOGI(TAG, "##   ██╔════╝    ██╔════╝        ██║        ##");
	    ESP_LOGI(TAG, "##   █████╗      ███████╗        ██║        ##");
	    ESP_LOGI(TAG, "##   ██╔══╝      ╚════██║        ██║        ##");
	    ESP_LOGI(TAG, "##   ██║         ███████║        ███████╗   ##");
	    ESP_LOGI(TAG, "##   ╚═╝         ╚══════╝        ╚══════╝   ##");
	    ESP_LOGI(TAG, "##############################################");

	    // 等待一小段时间确保日志完全输出
	    vTaskDelay(20 / portTICK_RATE_MS);
}

static void raw_gpio_input_init(void) {
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_INPUT,
        .pin_bit_mask = (1ULL << RAW_CLK) | (1ULL << RAW_READY),
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE
    };
    gpio_config(&io_conf);
}

static void radar_pwr_ctl_init(void) {
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_DISABLE,
        .mode = GPIO_MODE_OUTPUT,
        .pin_bit_mask = (1ULL << RADAR_PWR_CTL),
        .pull_up_en = GPIO_PULLUP_DISABLE,
        .pull_down_en = GPIO_PULLDOWN_DISABLE
    };
    gpio_config(&io_conf);
    // 默认拉高或拉低可根据实际需求设置
    gpio_set_level(RADAR_PWR_CTL, 1); // 默认关闭供电（高电平），如需上电可设为0
}


static void radar_rst_gpio_init(void) {
	gpio_config_t io_conf={
		.intr_type=GPIO_INTR_DISABLE,
		.pull_up_en=GPIO_PULLUP_DISABLE,
		.mode=GPIO_MODE_OUTPUT,
		.pin_bit_mask=(1<<RADAR_RST_PIN)
	};
	gpio_config(&io_conf);
	radar_reset();
	ESP_LOGI(TAG, "radar init finsh!");
}


