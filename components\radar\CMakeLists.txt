# 雷达组件主 CMakeLists.txt

# 添加子组件
# add_subdirectory(platform)
# add_subdirectory(driver)
# add_subdirectory(comm)
# add_subdirectory(config)

# 注册组件
idf_component_register(
    SRCS "platform/src/platform.c"
         "platform/src/bsp_esp32_uart.c"
         "platform/src/bsp_esp32_spi.c"
         "platform/src/bsp_esp32_nvs_flash.c"
         "platform/src/bsp_esp32_iic.c"
         "driver/src/banyan.c"
         "config/src/radar_config.c"
         "comm/src/system.c"
         "comm/src/utilities.c"
         "comm/src/dataprocess.c"
         "comm/src/cmdprocess.c"

    INCLUDE_DIRS    "platform/inc"
                    "driver/inc"
                    "config/inc"
                    "comm/inc"
                    
    REQUIRES nvs_flash led
)

