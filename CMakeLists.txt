# The following five lines of boilerplate have to be in your project's
# CMakeLists in this exact order for cmake to work correctly
cmake_minimum_required(VERSION 3.5)

set(EXTRA_COMPONENT_DIRS $ENV{IDF_PATH}/examples/common_components/led_strip components)

# 指定自定义分区表文件
set(PARTITION_TABLE_CSV "partitions.csv")
add_compile_definitions(EVBKS5_E)
add_compile_definitions(ESP32_PLATFORM)
include($ENV{IDF_PATH}/tools/cmake/project.cmake)
project(blink)
