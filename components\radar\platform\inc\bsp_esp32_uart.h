/*
 * bsp_esp32_uart.h
 *
 * ESP32 UART驱动接口，支持回调函数机制
 *
 * Created on: 2023年9月5日
 * Updated on: 2024年5月21日
 */

#ifndef PLATFORM_INC_BSP_ESP32_UART_H_
#define PLATFORM_INC_BSP_ESP32_UART_H_
#ifdef __cplusplus
 extern "C" {
#endif
#include "driver/gpio.h"
#include "driver/uart.h"

// 默认UART参数
#define USART0_BAUDRATE     (115200U)
#define APP_RX_DATA_SIZE  1024
#define CMD_RECV_BUF_MAX   (8)  // 减小缓冲区数量，原值为24
#define CMD_RECV_TIMEOUT    (5) /*ms*/


// 命令接收结构体
typedef struct CMD_RECV
{
    uint8_t buf[CMD_RECV_BUF_MAX][APP_RX_DATA_SIZE];
    volatile uint8_t bufRecv;
    volatile uint8_t bufProc;
    volatile uint8_t cmdReady;
    volatile uint16_t bufLen;
}CMD_RECV_T;

// 命令数据结构体
typedef struct {
    uint8_t *buf;      // 数据缓冲区指针
    uint16_t len;      // 数据长度
} CMD_DATA_T;

extern CMD_RECV_T g_cmdRecv;
// 回调函数类型定义
typedef void (*uart_cmd_recv_callback_t)(void *cmd_data);
typedef void (*uart_tx_complete_callback_t)(void);

/**
 * @brief 注册UART命令接收回调函数
 * @param callback 回调函数指针
 */
void bsp_uart_register_cmd_callback(uart_cmd_recv_callback_t callback);


/**
 * @brief 初始化UART0
 * @param baud_rate 波特率
 */
void bsp_uart0_init(uint32_t baud_rate);


#ifdef __cplusplus
}
#endif

#endif /* PLATFORM_INC_BSP_ESP32_UART_H_ */
