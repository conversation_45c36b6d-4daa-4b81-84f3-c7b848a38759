# ESP-IDF/VSCode/PlatformIO/CLion/JetBrains/Windows/Linux/MacOS 常用忽略
build/
managed_components/
*.bin
*.elf
*.map
*.pyc
*.pyo
*.swp
*.swo
*.swn
*.bak
*.log
*.orig
*.rej
*.out
*.o
*.d
*.a
*.so
*.DS_Store
*.vscode/
.idea/
*.user
*.sublime*
*.cproject
*.project
*.settings/
*.launch
sdkconfig
sdkconfig.old
sdkconfig.defaults
sdkconfig.ci
sdkconfig.*.bak
*.env
*.pyc
__pycache__/
*.zip
*.tar.gz
*.gz
*.7z
*.tmp
*.patch
*.diff
*.csv
*.md.bak
*.log.bak
*.log.old
*.log.*
*.bak.*
*.orig.*
*.rej.*
*.out.*
*.o.*
*.d.*
*.a.*
*.so.*
*.DS_Store.*
*.swp.*
*.swo.*
*.swn.*
*.user.*
*.sublime*.*
*.cproject.*
*.project.*
*.settings*.*
*.launch.*
*.env.*
*.pyc.*
__pycache__/*
# 备份和临时文件
*~
*.tmp
*.bak
*.old
*.orig
*.rej
# 忽略 partition_table 生成的内容
partition_table/*.bin
partition_table/*.csv.bak