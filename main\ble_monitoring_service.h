/**
 * @file ble_monitoring_service.h
 * @brief BLE Monitoring Service for different modes
 * @version 1.0
 * @date 2024-08-16
 */

#ifndef BLE_MONITORING_SERVICE_H
#define BLE_MONITORING_SERVICE_H

#include <stdbool.h>
#include "esp_err.h"
#include "host/ble_hs.h"

#ifdef __cplusplus
extern "C" {
#endif

// Monitoring mode definitions (same as in ble_config_service.h)
typedef enum {
    MONITOR_MODE_RAW_DATA = 0x01,           // 原始数据采集模式
    MONITOR_MODE_VITALS_MONITOR = 0x02,     // 生命体征监测模式
    MONITOR_MODE_FALL_DETECTION = 0x03,     // 跌倒检测功能模式
    MONITOR_MODE_PRESENCE_DETECTION = 0x04, // 人体存在检测模式
} ble_monitor_mode_t;

// Data structures for different monitoring modes
typedef struct {
    uint16_t heart_rate;       // 心率
    uint16_t respiratory_rate; // 呼吸率
} vitals_data_t;

typedef struct {
    uint8_t status;            // 状态 (0=正常, 1=检测到跌倒)
} fall_data_t;

typedef struct {
    uint8_t status;            // 状态 (0=无人, 1=有人)
    uint16_t distance;         // 距离，单位为厘米
} presence_data_t;

typedef struct {
    uint16_t light;            // 光线传感器值，单位为lux
    uint8_t pir;               // PIR传感器状态 (0=无人, 1=有人)
    int16_t temperature;       // 芯片温度，单位为摄氏度的10倍
} additional_sensors_data_t;

/** 
 * @brief Initialize the BLE Monitoring Service
 * 
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_monitoring_service_init(void);

/**
 * @brief Register callback for GATT service registration
 * 
 * @param ctxt GATT registration context
 * @param arg User argument
 */
void ble_monitoring_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg);

/**
 * @brief Update vitals monitoring data
 * 
 * @param data Vitals data to send
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_monitoring_update_vitals(vitals_data_t *data);

/**
 * @brief Update fall detection data
 * 
 * @param data Fall detection data to send
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_monitoring_update_fall(fall_data_t *data);

/**
 * @brief Update presence detection data
 * 
 * @param data Presence detection data to send
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_monitoring_update_presence(presence_data_t *data);

/**
 * @brief Update additional sensors data
 * 
 * @param data Additional sensors data to send
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_monitoring_update_sensors(additional_sensors_data_t *data);

/**
 * @brief Send simulated data based on the current mode
 * 
 * This function should be called periodically to send simulated data
 * based on the current monitoring mode.
 * 
 * @param mode Current monitoring mode
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_monitoring_send_simulated_data(ble_monitor_mode_t mode);

#ifdef __cplusplus
}
#endif

#endif /* BLE_MONITORING_SERVICE_H */
