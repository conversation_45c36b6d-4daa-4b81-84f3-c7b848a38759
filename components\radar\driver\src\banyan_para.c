
static RADAR_REG_T InitChipRegListConfig0[MAX_REG_NUM] __attribute__((aligned (4))) =
{
    {0x42, 0x0000},
    {0x43, 0x6978},
    {0x44, 0x7C40},
    {0x45, 0x0000},
    {0x46, 0x01F4},
    {0x47, 0x1000},
    {0x48, 0x1482},
    {0x49, 0x2000},
    {0x4A, 0x157C},
    {0x4B, 0x0000},
    {0x4C, 0x3D86},
    {0x4D, 0x0000},
    {0x4E, 0x0001},
    {0x4F, 0x0000},
    {0x50, 0x9C40},
    {0x51, 0x000B},
    {0x52, 0x2B59},
    {0x53, 0x5015},
    {0x54, 0x5556},
    {0x55, 0x0000},
    {0x56, 0x08F2},
    {0x57, 0xFFFF},
    {0x58, 0xF776},
    {0x59, 0x0000},
    {0x5A, 0x0000},
    {0x5B, 0x0022},
    {0x5C, 0x0022},
    {0x5D, 0x0501},
    {0x5E, 0xFF12},
    {0x5F, 0x2D16},
    {0x61, 0x0108},
    {0x62, 0x0088},
    {0x63, 0x0108},
    {0x64, 0x0088},
    {0x65, 0x5555},
    {0x66, 0x0000},
    {0x67, 0x0000},
    {0x6C, 0x8660},
    {0x6D, 0xE4C0},
    {0x6E, 0x03FC},
    {0x70, 0x2EA0},
    {0x76, 0x0021},
    {0x77, 0x160F},
    {0x06, 0x0122},
    {0x31, 0x0000},
    {0x07, 0x01B2},
    {0x08, 0x001C},
    {0x02, 0x000D},
    {0x04, 0x020C},
    {0x09, 0x6901},
    {0x35, 0x0900},
    {0x0B, 0x0C0D},
    {0x05, 0x0019},
    {0x0E, 0x0000},
    {0x0D, 0x4040},
    {0x33, 0x3F00},
    {0x34, 0x0F00},
    {0x32, 0x0000},
    {0x3C, 0x0004},
    {0x3B, 0x0801},
    {0x3E, 0x1019},
    {0x3F, 0x0470},
    {0x3A, 0x8418},
    {0x36, 0x00A3},
    {0x37, 0x0406},
    {0x3C, 0x1008},
    {0xFF, 0xFFFF} /*must be last, do not delete!!!*/
};
static RADAR_REG_T InitChipRegListStart0[MAX_REG_FIX_NUM] __attribute__((aligned (4))) =
{
    {0x72, 0x02D0},
    {0x41, 0x4004},
    {0x00, 0x2000},
    {0x01, 0x8DE4},
    {0x40, 0x0207},
    {0xFF, 0xFFFF} /*must be last, do not delete!!!*/
};


