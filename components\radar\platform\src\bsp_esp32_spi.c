/*
 * bsp_esp32_spi.c
 *
 * 实现ESP32 SPI从机接口，用于接收雷达数据
 * 支持双通道SPI接收和DMA双缓冲模式
 *
 * Created on: 2023年9月5日
 * Updated on: 2024年5月21日
 */

#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "bsp_esp32_spi.h"
#include "driver/spi_slave.h"
#include "esp_log.h"
#include "freertos/event_groups.h"
#include "freertos/task.h"

//雷达是master, esp32是从机
static const char *TAG = "bsp_esp32_spi.c";

// 四字节对齐的数据接收缓冲区
__ALIGN(4) uint8_t g_dataRecvBuf[CHANNEL_MAX][DATA_RECV_BUF_SIZE];
volatile uint8_t g_dataRecvFlag[CHANNEL_MAX][DMA_RECV_FLAG_MAX];
// sysPara.useMcuPackWrapper帧头
volatile static uint8_t g_useMcuPackWrapper = false;


// SPI使能标志
volatile static uint8_t sg_spi_enable = 0;
//真正一帧的长度
volatile static uint16_t curSpiFrameLen = 0;

// SPI传输结构体
typedef struct {
    spi_slave_transaction_t trans;
    uint8_t channel;
    uint8_t dma_flag;
} spi_slave_task_trans_t;

// SPI传输任务句柄
static TaskHandle_t spi_task_handle = NULL;


// SPI设备句柄
static spi_host_device_t spi_host[CHANNEL_MAX] = {SPI2_HOST, SPI3_HOST};

// SPI引脚定义
#define GPIO_MOSI_0      11  // 通道0 MOSI引脚
#define GPIO_MOSI_1      10  // 通道1 MOSI引脚
#define GPIO_SCLK_0      15  // 通道0 SCLK引脚
#define GPIO_SCLK_1      16  // 通道1 SCLK引脚
#define GPIO_CS_0        13  // 通道0 CS引脚
#define GPIO_CS_1        14  // 通道1 CS引脚
#define GPIO_MISO_0      -1  // 通道0 MISO引脚 (不使用)
#define GPIO_MISO_1      -1  // 通道1 MISO引脚 (不使用)


//备用，目前用不上
#define RAW_CLK             17
#define RAW_READY           12
#define RAW_D0              GPIO_CS_0 //or GPIO_CS_1
#define RAW_D1              GPIO_MOSI_0
#define RAW_D2              GPIO_MOSI_1
#define RAW_D3              GPIO_SCLK_0 //or GPIO_SCLK_1

// DMA缓冲区长度
#define SPI_DMA_BUFF_LEN SPI_FRAME_LEN_MAX

spi_data_recv_callback_t g_spi_recv_callback = NULL;
// SPI传输任务
/**
 * @brief 注册SPI数据接收回调函数
 * @param callback 回调函数指针
 */
void bsp_spi_register_recv_callback(spi_data_recv_callback_t callback)
{
    // 注册回调函数
    g_spi_recv_callback = callback;
}

/**
 * @brief SPI传输任务
 *
 * @param pvParameters 任务参数
 */
static void spi_trans_task(void *pvParameters)
{
    uint8_t spidatabuff[CHANNEL_MAX][curSpiFrameLen];
    uint8_t index = 0;
    esp_err_t ret;
    spi_slave_transaction_t trans0 = {
        .tx_buffer = NULL, // 从机通常不需要发送数据
        .rx_buffer = spidatabuff[0],
        .length = curSpiFrameLen * 8, // 数据长度（以位为单位）
    };
    spi_slave_transaction_t trans1 = {
        .tx_buffer = NULL, // 从机通常不需要发送数据
        .rx_buffer = spidatabuff[1],
        .length = curSpiFrameLen * 8, // 数据长度（以位为单位）
    };
    ESP_LOGI(TAG, "SPI传输任务启动");

    while (1) {
        // 从队列中获取传输请求
        if(sg_spi_enable == 0) {
            // SPI未使能，等待
            ESP_LOGI(TAG, "SPI未使能，等待...");
            
            // 等待接收数据
            vTaskDelay(1000 / portTICK_PERIOD_MS);
            continue;
        }
        // 执行SPI传输
        ret = spi_slave_transmit(spi_host[0], &trans0, portMAX_DELAY);
        // ret = spi_slave_transmit(spi_host[1], &trans1, portMAX_DELAY);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "SPI传输失败，错误: %s",  esp_err_to_name(ret));
        }
        memcpy(&g_dataRecvBuf[0][index], spidatabuff[0], curSpiFrameLen);
        // memcpy(&g_dataRecvBuf[1][index], spidatabuff[1], curSpiFrameLen);
        if (index == 0)
        {
            g_dataRecvFlag[0][0] = 1;
            // g_dataRecvFlag[1][0] = 1;
            index = curSpiFrameLen;
        }
        else
        {
            g_dataRecvFlag[0][1] = 1;
            // g_dataRecvFlag[1][1] = 1;
            index = 0;
        }
        g_spi_recv_callback(); //DataProc_Recv();
        // ESP_LOGI(TAG,"buff[0]=%x, buff[71]=%x",g_dataRecvBuf[channel][0],g_dataRecvBuf[channel][71]);
        // 准备下一次传输

    }
}

/**
 * @brief 配置SPI从机
 *
 * @param channel SPI通道
 * @return esp_err_t 错误码
 */
static esp_err_t spi_slave_config(uint8_t channel)
{
    esp_err_t ret;

    // 选择引脚
    int mosi_pin, sclk_pin, cs_pin;
    if (channel == 0) {
        mosi_pin = GPIO_MOSI_0;
        sclk_pin = GPIO_SCLK_0;
        cs_pin = GPIO_CS_0;
    } else {
        mosi_pin = GPIO_MOSI_1;
        sclk_pin = GPIO_SCLK_1;
        cs_pin = GPIO_CS_1;
    }

    // 配置SPI总线
    spi_bus_config_t buscfg = {
        .mosi_io_num = mosi_pin,
        .miso_io_num = -1,  // 不使用MISO
        .sclk_io_num = sclk_pin,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = SPI_DMA_BUFF_LEN,
        .flags = SPICOMMON_BUSFLAG_SLAVE,
    };

    // 配置SPI从机接口
    spi_slave_interface_config_t slvcfg = {
        .mode = 0,
        .spics_io_num = cs_pin,
        .queue_size = 3,
        .flags = 0,
        .post_setup_cb = NULL,
        .post_trans_cb = NULL
    };

    // 启用SPI引脚上拉电阻，防止在主机未连接时检测到错误脉冲
    gpio_set_pull_mode(mosi_pin, GPIO_PULLUP_ONLY);
    gpio_set_pull_mode(sclk_pin, GPIO_PULLUP_ONLY);
    gpio_set_pull_mode(cs_pin, GPIO_PULLUP_ONLY);

    // 初始化SPI从机接口
    ESP_ERROR_CHECK(spi_slave_initialize(spi_host[channel], &buscfg, &slvcfg, SPI_DMA_CH_AUTO));



    ESP_LOGI(TAG, "SPI从机配置成功，通道: %d", channel);
    return ESP_OK;
}

/**
 * @brief 初始化SPI从机
 */
void bsp_spi_slave_init(uint16_t dmaBufLen, bool useMcuPackWrapper)
{
    g_useMcuPackWrapper = useMcuPackWrapper;
    curSpiFrameLen = dmaBufLen >> 1;
    if (curSpiFrameLen > SPI_FRAME_LEN_MAX) {
        ESP_LOGE(TAG, "SPI帧长度超过最大值");
        return;
    }
    ESP_LOGI(TAG, "初始化SPI从机...");

    // 清空接收缓冲区和标志
    memset((void *)g_dataRecvBuf, 0, sizeof(g_dataRecvBuf));
    memset((void *)g_dataRecvFlag, 0, sizeof(g_dataRecvFlag));


    // 配置通道0 SPI从机
    esp_err_t ret = spi_slave_config(0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置通道0 SPI从机失败");
        return;
    }

    // 配置通道1 SPI从机
    ret = spi_slave_config(1);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置通道1 SPI从机失败");
        // 释放通道0资源
        if (spi_task_handle != NULL) {
            vTaskDelete(spi_task_handle);
            spi_task_handle= NULL;
        }
        spi_slave_free(spi_host[0]);
        return;
    }

    sg_spi_enable = 1;
    ESP_LOGI(TAG, "SPI从机初始化成功");

    // 创建SPI传输任务

    BaseType_t xReturn = xTaskCreate(spi_trans_task, "spi_trans_task", 4096, NULL, configMAX_PRIORITIES - 2, &spi_task_handle);
    if (xReturn != pdPASS) {
        ESP_LOGE(TAG, "创建SPI传输任务失败");
        spi_slave_free(spi_host[0]);
        spi_slave_free(spi_host[1]);
        return ESP_FAIL;
    }
}

/**
 * @brief 反初始化SPI从机
 */
void bsp_spi_deinit(void)
{
    if (sg_spi_enable == 0) {
        return;
    }

    ESP_LOGI(TAG, "反初始化SPI从机...");

    // 删除SPI传输任务
    if (spi_task_handle != NULL) {
        vTaskDelete(spi_task_handle);
        spi_task_handle = NULL;
    }

    // 释放SPI从机
    spi_slave_free(spi_host[0]);
    spi_slave_free(spi_host[1]);

    sg_spi_enable = 0;
    ESP_LOGI(TAG, "SPI从机反初始化成功");
}
