
static RADAR_REG_T InitChipRegListConfig0[MAX_REG_NUM] __attribute__((aligned (4))) =
{
    {0x40, 0x4207},
    {0x41, 0x0000},
    {0x09, 0xE901},
    {0x01, 0x0000},
    {0x67, 0x0000},
    {0x72, 0x0650},
    {0x3A, 0x8410},
    {0x77, 0x3200},
    {0x42, 0x0000},
    {0x43, 0x61A8},
    {0x44, 0x7C80},
    {0x45, 0x0000},
    {0x46, 0x01F4},
    {0x47, 0x1000},
    {0x48, 0x2904},
    {0x49, 0x2000},
    {0x4A, 0x0EA6},
    {0x4B, 0x0000},
    {0x4C, 0x280A},
    {0x4D, 0x0000},
    {0x4E, 0x0001},
    {0x4F, 0x0000},
    {0x50, 0x01F4},
    {0x51, 0x0000},
    {0x52, 0x0765},
    {0x53, 0x50D5},
    {0x54, 0x5556},
    {0x55, 0x0000},
    {0x56, 0x0533},
    {0x57, 0xFFFF},
    {0x58, 0xF16F},
    {0x59, 0x0000},
    {0x5A, 0x0000},
    {0x5B, 0x0022},
    {0x5C, 0x0022},
    {0x5D, 0x0601},
    {0x5E, 0xFF12},
    {0x5F, 0x2D16},
    {0x61, 0x0108},
    {0x62, 0x0088},
    {0x63, 0x0108},
    {0x64, 0x0088},
    {0x65, 0x5555},
    {0x66, 0x0000},
    {0x67, 0x0000},
    {0x6C, 0xC640},
    {0x6D, 0xECC0},
    {0x6E, 0x03FC},
    {0x70, 0x36A0},
    {0x76, 0x0001},
    {0x77, 0x260F},
    {0x06, 0x0122},
    {0x31, 0x0000},
    {0x07, 0x01B2},
    {0x08, 0x001C},
    {0x02, 0x000F},
    {0x04, 0x020C},
    {0x09, 0x6901},
    {0x35, 0x0000},
    {0x0B, 0x0C0F},
    {0x05, 0x0010},
    {0x0E, 0x0000},
    {0x0D, 0x4080},
    {0x33, 0x7F00},
    {0x34, 0x0700},
    {0x32, 0x0000},
    {0x3C, 0x0004},
    {0x3B, 0x0801},
    {0x3E, 0x1011},
    {0x3F, 0x05D0},
    {0x3A, 0x8618},
    {0x36, 0x00A3},
    {0x37, 0x0406},
    {0x3C, 0x1008},
    {0xFF, 0xFFFF} /*must be last, do not delete!!!*/
};
static RADAR_REG_T InitChipRegListStart0[MAX_REG_FIX_NUM] __attribute__((aligned (4))) =
{
    {0x72, 0x02D0},
    {0x41, 0x4004},
    {0x00, 0x2000},
    {0x01, 0x8DE4},
    {0x40, 0x0207},
    {0xFF, 0xFFFF} /*must be last, do not delete!!!*/
};


