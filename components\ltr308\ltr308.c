#include "ltr308.h"
#include <stdio.h>
#include <string.h>
#include "driver/i2c.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"

#define TAG "LTR308"

#ifndef LTR308_I2C_PORT
#define LTR308_I2C_PORT I2C_NUM_0
#endif
#ifndef LTR308_I2C_SDA
#define LTR308_I2C_SDA 2
#endif
#ifndef LTR308_I2C_SCL
#define LTR308_I2C_SCL 3
#endif

#ifndef LTR308_ISR_PIN
#define LTR308_ISR_PIN 40
#endif

static uint8_t ltr308_i2c_addr = LTR308_ADDR;
static int ltr308_i2c_port = LTR308_I2C_PORT;
static ltr308_gain_t current_gain = LTR308_GAIN_3X;
static ltr308_resolution_t current_resolution = LTR308_RES_100MS_18BIT;
static ltr308_meas_rate_t current_meas_rate = LTR308_RATE_100MS;

static QueueHandle_t gpio_evt_queue = NULL;
static bool is_isr_initialized = false;
// 中断处理任务
static void ltr308_interrupt_task(void* arg)
{
    uint32_t io_num;
    while(1) {
        if(xQueueReceive(gpio_evt_queue, &io_num, portMAX_DELAY)) {
            ESP_LOGI(TAG, "GPIO[%u] 中断触发", io_num);

            // 获取传感器状态确认中断
            ltr308_status_t main_status;
            ltr308_get_status(&main_status);

            if (main_status.intr_status) {
                ESP_LOGI(TAG, "LTR308中断触发...");
                ltr308_set_power_down();

                // 如果停止，等待5秒后重新开始
                vTaskDelay(5000 / portTICK_PERIOD_MS);
                ltr308_set_power_up();
                ESP_LOGI(TAG, "............................");
                ESP_LOGI(TAG, "重新采集...");
            }
        }
    }
}

/************************************************************************
 @名称：ltr308_i2c_write_reg
 @功能：写寄存器
 @参数：reg，寄存器地址；data，数据；len，数据长度
 @返回：ESP_OK成功，其他失败
*************************************************************************/
static esp_err_t ltr308_i2c_write_reg(uint8_t reg, const uint8_t *data, size_t len) {
    if (data == NULL) {
        ESP_LOGE(TAG, "写入数据为空指针");
        return ESP_FAIL;
    }

    uint8_t *write_buf = (uint8_t *)malloc(len + 1);
    if (write_buf == NULL) {
        ESP_LOGE(TAG, "内存分配失败");
        return ESP_FAIL;
    }

    write_buf[0] = reg;
    memcpy(write_buf + 1, data, len);

    esp_err_t ret = i2c_master_write_to_device(ltr308_i2c_port, ltr308_i2c_addr, write_buf, len + 1, 1000);
    free(write_buf);

    return ret;
}

/************************************************************************
 @名称：ltr308_i2c_read_reg
 @功能：读寄存器
 @参数：reg，寄存器地址；data，数据缓冲区；len，数据长度
 @返回：ESP_OK成功，其他失败
*************************************************************************/
static esp_err_t ltr308_i2c_read_reg(uint8_t reg, uint8_t *data, size_t len) {
    return i2c_master_write_read_device(ltr308_i2c_port, ltr308_i2c_addr, &reg, 1, data, len, 1000);
}

// 中断处理函数
static void IRAM_ATTR ltr308_interrupt_handler(void* arg) {
    int gpio_num = (int)arg;
    xQueueSendFromISR(gpio_evt_queue, &gpio_num, NULL);
}

/************************************************************************
 @名称：ltr308_setup_interrupt_pin
 @功能：设置中断引脚
 @参数：gpio_num，GPIO引脚号
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_setup_interrupt_pin(int gpio_num) {
    // 创建中断队列（如果尚未创建）
    if (gpio_evt_queue == NULL) {
        gpio_evt_queue = xQueueCreate(10, sizeof(uint32_t));
        if (gpio_evt_queue == NULL) {
            ESP_LOGE(TAG, "创建中断队列失败");
            return -1;
        }
    }

    // 配置GPIO
    gpio_config_t io_conf = {
        .pin_bit_mask = (1ULL << gpio_num),    // 选择GPIO引脚
        .mode = GPIO_MODE_INPUT,               // 设置为输入模式
        .pull_up_en = GPIO_PULLUP_ENABLE,      // 启用上拉电阻
        .pull_down_en = GPIO_PULLDOWN_DISABLE, // 禁用下拉电阻
        .intr_type = GPIO_INTR_NEGEDGE,        // 下降沿触发中断（根据LTR308的中断输出特性调整）
    };

    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GPIO配置失败");
        return -2;
    }

    // 安装GPIO中断服务
    ret = gpio_install_isr_service(0);
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
        ESP_LOGE(TAG, "安装GPIO中断服务失败");
        return -3;
    }

    // 添加中断处理函数
    ret = gpio_isr_handler_add(gpio_num, ltr308_interrupt_handler, (void*)gpio_num);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "添加中断处理函数失败");
        return -4;
    }

    // 如果中断任务尚未创建，则创建它
    if (!is_isr_initialized) {
        BaseType_t task_created = xTaskCreate(ltr308_interrupt_task, "ltr308_interrupt_task", 2048, NULL, 10, NULL);
        if (task_created != pdPASS) {
            ESP_LOGE(TAG, "创建中断处理任务失败");
            return -5;
        }
        is_isr_initialized = true;
    }

    ESP_LOGI(TAG, "中断引脚设置成功: GPIO%d", gpio_num);
    return 0;
}

/************************************************************************
 @名称：ltr308_init
 @功能：初始化LTR308传感器
 @参数：config，传感器配置
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_init(const ltr308_config_t *config) {
    ESP_LOGI(TAG, "正在初始化LTR308传感器...");
    if (config) {
        ltr308_i2c_addr = config->i2c_addr;
        ltr308_i2c_port = config->i2c_port;
    }

    // I2C初始化（如已初始化可跳过）
    i2c_config_t i2c_conf = {
        .mode = I2C_MODE_MASTER,
        .sda_io_num = config ? config->sda_pin : LTR308_I2C_SDA,
        .scl_io_num = config ? config->scl_pin : LTR308_I2C_SCL,
        .sda_pullup_en = GPIO_PULLUP_ENABLE,
        .scl_pullup_en = GPIO_PULLUP_ENABLE,
        .master.clk_speed = 400000
    };

    esp_err_t ret = i2c_param_config(ltr308_i2c_port, &i2c_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C参数配置失败");
        return -1;
    }

    ret = i2c_driver_install(ltr308_i2c_port, I2C_MODE_MASTER, 0, 0, 0);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "I2C驱动安装失败");
        return -2;
    }

    // 等待传感器启动
    vTaskDelay(50 / portTICK_PERIOD_MS);

    // 检查传感器ID
    uint8_t id = ltr308_get_part_id();
    if (id != LTR308_PART_V_ID) {
        ESP_LOGE(TAG, "传感器ID不匹配: 0x%02X", id);
        return -3;
    }

    // 先断电
    if (ltr308_set_power_down() != 0) {
        ESP_LOGE(TAG, "传感器上电失败");
        return -4;
    }

    vTaskDelay(10 / portTICK_PERIOD_MS);

    // 设置默认增益
    if (ltr308_set_gain(current_gain) != 0) {
        ESP_LOGE(TAG, "设置增益失败");
        return -5;
    }

    // 设置默认测量速率和分辨率
    if (ltr308_set_measurement_rate_separate(current_resolution, current_meas_rate) != 0) {
        ESP_LOGE(TAG, "设置测量速率和分辨率失败");
        return -6;
    }
    // 设置中断引脚（如果配置中指定了）
    int isr_pin = config ? config->isr_pin : LTR308_ISR_PIN;
    if (isr_pin >= 0) {
        ltr308_setup_interrupt_pin(isr_pin);
         //TODO：传感器测得的光照值高于上限或低于下限时，会触发中断。具体值应该有环境关设定
        ltr308_set_threshold_separate(50, 1000);
        ltr308_set_intr_persist(LTR308_INTR_TRIG_1); // 改为1次超出阈值就触发
        ltr308_set_interrupt_control(true);

    }

    // 上电 配置生效
    if (ltr308_set_power_up() != 0) {
        ESP_LOGE(TAG, "传感器上电失败");
        return -4;
    }
    ESP_LOGI(TAG, "LTR308初始化成功");
    return 0;
}

/************************************************************************
 @名称：ltr308_set_power_up
 @功能：设置传感器上电
 @参数：无
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_power_up(void) {
    uint8_t data = 0x02; // 激活模式
    return ltr308_i2c_write_reg(LTR308_REG_CONTR, &data, 1) == ESP_OK ? 0 : -1;
}

/************************************************************************
 @名称：ltr308_set_power_down
 @功能：设置传感器断电
 @参数：无
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_power_down(void) {
    uint8_t data = 0x00; // 断电模式
    return ltr308_i2c_write_reg(LTR308_REG_CONTR, &data, 1) == ESP_OK ? 0 : -1;
}

/************************************************************************
 @名称：ltr308_get_power
 @功能：获取传感器电源状态
 @参数：无
 @返回：电源控制寄存器值
*************************************************************************/
uint8_t ltr308_get_power(void) {
    uint8_t data = 0x00;
    if (ltr308_i2c_read_reg(LTR308_REG_CONTR, &data, 1) != ESP_OK) {
        ESP_LOGE(TAG, "读取电源状态失败");
        return 0;
    }
    return data;
}

/************************************************************************
 @名称：ltr308_set_gain
 @功能：设置传感器增益
 @参数：gain，增益值
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_gain(ltr308_gain_t gain) {
    if (gain > LTR308_GAIN_18X) {
        ESP_LOGE(TAG, "增益值超出范围");
        gain = LTR308_GAIN_1X;
    }

    uint8_t data = (uint8_t)gain;
    if (ltr308_i2c_write_reg(LTR308_REG_ALS_GAIN, &data, 1) != ESP_OK) {
        ESP_LOGE(TAG, "设置增益失败");
        return -1;
    }

    current_gain = gain;
    return 0;
}

/************************************************************************
 @名称：ltr308_get_gain
 @功能：获取传感器增益
 @参数：无
 @返回：增益值
*************************************************************************/
uint8_t ltr308_get_gain(void) {
    uint8_t gain = 0x00;
    if (ltr308_i2c_read_reg(LTR308_REG_ALS_GAIN, &gain, 1) != ESP_OK) {
        ESP_LOGE(TAG, "读取增益失败");
        return 0;
    }
    return gain;
}

/************************************************************************
 @名称：ltr308_set_measurement_rate
 @功能：设置测量速率和分辨率
 @参数：meas_config，测量配置
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_measurement_rate(ltr308_meas_config_t meas_config) {
    if (meas_config.resolution > LTR308_RES_25MS_16BIT) {
        ESP_LOGE(TAG, "分辨率值超出范围");
        meas_config.resolution = LTR308_RES_25MS_16BIT;
    }

    if (meas_config.meas_rate > LTR308_RATE_2000MS_2 || meas_config.meas_rate == 4) {
        ESP_LOGE(TAG, "测量速率值超出范围");
        meas_config.meas_rate = LTR308_RATE_2000MS_2;
    }

    uint8_t data = ((uint8_t)meas_config.resolution << 4) | (uint8_t)meas_config.meas_rate;
    if (ltr308_i2c_write_reg(LTR308_REG_MEAS_RATE, &data, 1) != ESP_OK) {
        ESP_LOGE(TAG, "设置测量速率和分辨率失败");
        return -1;
    }

    current_resolution = meas_config.resolution;
    current_meas_rate = meas_config.meas_rate;
    return 0;
}

/************************************************************************
 @名称：ltr308_set_measurement_rate_separate
 @功能：分别设置测量速率和分辨率
 @参数：resolution，分辨率；meas_rate，测量速率
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_measurement_rate_separate(ltr308_resolution_t resolution, ltr308_meas_rate_t meas_rate) {
    ltr308_meas_config_t meas_config = {
        .resolution = resolution,
        .meas_rate = meas_rate
    };
    return ltr308_set_measurement_rate(meas_config);
}

/************************************************************************
 @名称：ltr308_get_measurement_rate
 @功能：获取测量速率和分辨率
 @参数：meas_config，用于存储获取的测量配置
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_get_measurement_rate(ltr308_meas_config_t *meas_config) {
    if (meas_config == NULL) {
        ESP_LOGE(TAG, "参数为空指针");
        return -1;
    }

    uint8_t data = 0x00;
    if (ltr308_i2c_read_reg(LTR308_REG_MEAS_RATE, &data, 1) != ESP_OK) {
        ESP_LOGE(TAG, "读取测量速率和分辨率失败");
        return -2;
    }

    meas_config->resolution = (ltr308_resolution_t)((data & 0x70) >> 4);
    meas_config->meas_rate = (ltr308_meas_rate_t)(data & 0x07);
    return 0;
}

/************************************************************************
 @名称：ltr308_get_part_id
 @功能：获取传感器ID
 @参数：无
 @返回：传感器ID
*************************************************************************/
uint8_t ltr308_get_part_id(void) {
    uint8_t part_id = 0;
    if (ltr308_i2c_read_reg(LTR308_REG_PART_ID, &part_id, 1) != ESP_OK) {
        ESP_LOGE(TAG, "读取传感器ID失败");
        return 0;
    }
    return part_id;
}

/************************************************************************
 @名称：ltr308_get_status
 @功能：获取传感器状态
 @参数：status，用于存储获取的状态
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_get_status(ltr308_status_t *status) {
    if (status == NULL) {
        ESP_LOGE(TAG, "参数为空指针");
        return -1;
    }

    uint8_t data = 0x00;
    if (ltr308_i2c_read_reg(LTR308_REG_STATUS, &data, 1) != ESP_OK) {
        ESP_LOGE(TAG, "读取状态失败");
        return -2;
    }

    status->pon_status = (data & 0x20) ? true : false;
    status->intr_status = (data & 0x10) ? true : false;
    status->data_status = (data & 0x08) ? true : false;
    return 0;
}

/************************************************************************
 @名称：ltr308_get_data
 @功能：获取传感器原始数据
 @参数：无
 @返回：传感器原始数据
*************************************************************************/
uint32_t ltr308_get_data(void) {
    uint8_t data[3] = {0};
    if (ltr308_i2c_read_reg(LTR308_REG_DATA_0, data, 3) != ESP_OK) {
        ESP_LOGE(TAG, "读取数据失败");
        return 0;
    }
    return ((uint32_t)(data[2] & 0x0F) << 16) | ((uint32_t)data[1] << 8) | (uint32_t)data[0];
}

/************************************************************************
 @名称：ltr308_set_interrupt_control
 @功能：设置中断控制
 @参数：mode，中断模式（true启用，false禁用）
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_interrupt_control(bool mode) {
    // 读取当前寄存器值
    uint8_t current_data = 0;
    if (ltr308_i2c_read_reg(LTR308_REG_INTERRUPT, &current_data, 1) != ESP_OK) {
        ESP_LOGE(TAG, "读取中断控制寄存器失败");
        return -1;
    }

    // 保留其他位，只修改中断使能位
    uint8_t data = (current_data & ~0x04) | (mode ? 0x04 : 0x00);

    // 确保位7-4为0，位3为1（中断使能），位2为0（保留），位1-0为0（保留）
    data = (data & 0x0F) | 0x10; // 确保位4为1，其他高位为0

    ESP_LOGI(TAG, "设置中断控制寄存器: 0x%02X -> 0x%02X", current_data, data);

    return ltr308_i2c_write_reg(LTR308_REG_INTERRUPT, &data, 1) == ESP_OK ? 0 : -1;
}

/************************************************************************
 @名称：ltr308_get_interrupt_control
 @功能：获取中断控制状态
 @参数：无
 @返回：中断控制状态（true启用，false禁用）
*************************************************************************/
bool ltr308_get_interrupt_control(void) {
    uint8_t data = 0x00;
    if (ltr308_i2c_read_reg(LTR308_REG_INTERRUPT, &data, 1) != ESP_OK) {
        ESP_LOGE(TAG, "读取中断控制状态失败");
        return false;
    }
    return (data & 0x04) ? true : false;
}

/************************************************************************
 @名称：ltr308_set_intr_persist
 @功能：设置中断持续性
 @参数：persist，中断持续性
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_intr_persist(ltr308_intr_persist_t persist) {
    uint8_t data = (uint8_t)persist << 4;
    return ltr308_i2c_write_reg(LTR308_REG_INTR_PERS, &data, 1) == ESP_OK ? 0 : -1;
}

/************************************************************************
 @名称：ltr308_get_intr_persist
 @功能：获取中断持续性
 @参数：无
 @返回：中断持续性值
*************************************************************************/
uint8_t ltr308_get_intr_persist(void) {
    uint8_t data = 0x00;
    if (ltr308_i2c_read_reg(LTR308_REG_INTR_PERS, &data, 1) != ESP_OK) {
        ESP_LOGE(TAG, "读取中断持续性失败");
        return 0;
    }
    return data >> 4;
}

/************************************************************************
 @名称：ltr308_set_threshold
 @功能：设置阈值
 @参数：threshold，阈值结构体
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_threshold(ltr308_threshold_t threshold) {
    uint8_t data[6] = {0};

    // 上限
    data[0] = threshold.upper_limit & 0xFF;
    data[1] = (threshold.upper_limit >> 8) & 0xFF;
    data[2] = (threshold.upper_limit >> 16) & 0x0F;

    // 下限
    data[3] = threshold.lower_limit & 0xFF;
    data[4] = (threshold.lower_limit >> 8) & 0xFF;
    data[5] = (threshold.lower_limit >> 16) & 0x0F;

    return ltr308_i2c_write_reg(LTR308_REG_THRES_UP_0, data, 6) == ESP_OK ? 0 : -1;
}

/************************************************************************
 @名称：ltr308_set_threshold_separate
 @功能：分别设置上下阈值
 @参数：upper_limit，上限；lower_limit，下限
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_set_threshold_separate(uint32_t upper_limit, uint32_t lower_limit) {
    ltr308_threshold_t threshold = {
        .upper_limit = upper_limit,
        .lower_limit = lower_limit
    };
    return ltr308_set_threshold(threshold);
}

/************************************************************************
 @名称：ltr308_get_threshold
 @功能：获取阈值
 @参数：threshold，用于存储获取的阈值
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_get_threshold(ltr308_threshold_t *threshold) {
    if (threshold == NULL) {
        ESP_LOGE(TAG, "参数为空指针");
        return -1;
    }

    uint8_t data[6] = {0};
    if (ltr308_i2c_read_reg(LTR308_REG_THRES_UP_0, data, 6) != ESP_OK) {
        ESP_LOGE(TAG, "读取阈值失败");
        return -2;
    }

    threshold->upper_limit = ((uint32_t)(data[2] & 0x0F) << 16) | ((uint32_t)data[1] << 8) | (uint32_t)data[0];
    threshold->lower_limit = ((uint32_t)(data[5] & 0x0F) << 16) | ((uint32_t)data[4] << 8) | (uint32_t)data[3];
    return 0;
}

/************************************************************************
 @名称：ltr308_get_lux
 @功能：获取光照强度（lux）
 @参数：gain，增益；resolution，分辨率；als_data，原始数据
 @返回：光照强度值
*************************************************************************/
double ltr308_get_lux(ltr308_gain_t gain, ltr308_resolution_t resolution, uint32_t als_data) {
    double lux = als_data * 0.6;

    // 根据增益调整
    switch (gain) {
        case LTR308_GAIN_1X:
            // 不变
            break;
        case LTR308_GAIN_3X:
            lux = lux / 3;
            break;
        case LTR308_GAIN_6X:
            lux = lux / 6;
            break;
        case LTR308_GAIN_9X:
            lux = lux / 9;
            break;
        case LTR308_GAIN_18X:
            lux = lux / 18;
            break;
        default:
            ESP_LOGE(TAG, "无效的增益值");
            return 0.0;
    }

    // 根据分辨率调整
    switch (resolution) {
        case LTR308_RES_400MS_20BIT:
            lux = lux / 4;
            break;
        case LTR308_RES_200MS_19BIT:
            lux = lux / 2;
            break;
        case LTR308_RES_100MS_18BIT:
            // 不变
            break;
        case LTR308_RES_50MS_17BIT:
            lux = lux * 2;
            break;
        case LTR308_RES_25MS_16BIT:
            lux = lux * 4;
            break;
        default:
            ESP_LOGE(TAG, "无效的分辨率值");
            return 0.0;
    }

    return lux;
}

/************************************************************************
 @名称：ltr308_get_lux_simple
 @功能：使用当前配置获取光照强度（lux）
 @参数：als_data，原始数据
 @返回：光照强度值
*************************************************************************/
double ltr308_get_lux_simple(uint32_t als_data) {
    return ltr308_get_lux(current_gain, current_resolution, als_data);
}

/************************************************************************
 @名称：ltr308_read_lux
 @功能：读取光照强度（lux）
 @参数：lux，用于存储光照强度值
 @返回：0成功，非0失败
*************************************************************************/
int ltr308_read_lux(uint32_t *lux) {
    if (lux == NULL) {
        ESP_LOGE(TAG, "参数为空指针");
        return -1;
    }

    uint32_t als_data = ltr308_get_data();
    double lux_value = ltr308_get_lux_simple(als_data);
    *lux = (uint32_t)lux_value;

    return 0;
}

/************************************************************************
 @名称：ltr308_read_interrupt_pin
 @功能：读取中断引脚状态
 @参数：isr_pin，中断引脚号
 @返回：引脚状态（0或1），-1表示错误
*************************************************************************/
int ltr308_read_interrupt_pin(int isr_pin) {
    static int _isr_pin = -1;

    // 如果还没有保存中断引脚号，尝试从配置中获取
    if (_isr_pin < 0) {
        _isr_pin = isr_pin;
    }

    if (_isr_pin < 0) {
        ESP_LOGE(TAG, "中断引脚未配置");
        return -1;
    }

    // 读取引脚状态
    return gpio_get_level(_isr_pin);
}
