/**
 * @file ble_ota_service.c
 * @brief BLE OTA Service Implementation
 * @version 1.0
 * @date 2024-08-10
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "esp_ota_ops.h"
#include "esp_partition.h"
#include "esp_system.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "host/ble_hs.h"
#include "host/ble_uuid.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include "ble_ota_service.h"

static const char *TAG = "BLE_OTA_SERVICE";

// // OTA Service UUID: Custom UUID for OTA service
// const ble_uuid128_t gatt_ota_svc_uuid =
//     BLE_UUID128_INIT(0xE0, 0xFF, 0x42, 0x63, 0xDE, 0x45, 0xA0, 0x88,
//                      0xCC, 0xB0, 0xDA, 0x45, 0xF0, 0x2E, 0x47, 0x42);

// 微信小程序使用的OTA服务UUID
// 42472EF0-45DA-B0CC-88A0-45DE6342FFE0
const ble_uuid128_t gatt_ota_svc_uuid_wx =
    BLE_UUID128_INIT(0xE0, 0xFF, 0x42, 0x63, 0xDE, 0x45, 0xA0, 0x88,
                     0xCC, 0xB0, 0xDA, 0x45, 0xF0, 0x2E, 0x47, 0x42);

// OTA Control Characteristic UUID
const ble_uuid128_t gatt_ota_control_chr_uuid =
    BLE_UUID128_INIT(0xE1, 0xFF, 0x42, 0x63, 0xDE, 0x45, 0xA0, 0x88,
                     0xCC, 0xB0, 0xDA, 0x45, 0xF0, 0x2E, 0x47, 0x42);

// OTA Data Characteristic UUID
const ble_uuid128_t gatt_ota_data_chr_uuid =
    BLE_UUID128_INIT(0xE2, 0xFF, 0x42, 0x63, 0xDE, 0x45, 0xA0, 0x88,
                     0xCC, 0xB0, 0xDA, 0x45, 0xF0, 0x2E, 0x47, 0x42);

// OTA service handle
static uint16_t ota_svc_handle;

// OTA control characteristic handle
static uint16_t ota_control_chr_handle;

// OTA data characteristic handle
static uint16_t ota_data_chr_handle;

// OTA state
static bool ota_in_progress = false;

// OTA update handle
static esp_ota_handle_t ota_handle = 0;

// OTA update partition
static const esp_partition_t *update_partition = NULL;

// OTA data buffer
static uint8_t ota_data_buffer[1024];
static size_t ota_data_buffer_size = 0;

// OTA semaphore for thread safety
static SemaphoreHandle_t ota_semaphore = NULL;

// Forward declarations
static int ble_ota_control_chr_access_cb(uint16_t conn_handle, uint16_t attr_handle,
                                        struct ble_gatt_access_ctxt *ctxt, void *arg);
static int ble_ota_data_chr_access_cb(uint16_t conn_handle, uint16_t attr_handle,
                                     struct ble_gatt_access_ctxt *ctxt, void *arg);
static void ble_ota_send_response(uint16_t conn_handle, ota_response_code_t code);
static esp_err_t ble_ota_start(void);
static esp_err_t ble_ota_end(void);
static esp_err_t ble_ota_abort(void);
static void ble_ota_reboot(void);

// OTA service definition
static const struct ble_gatt_svc_def ota_service_defs[] = {
    {
        // OTA Service
        .type = BLE_GATT_SVC_TYPE_PRIMARY,
        .uuid = &gatt_ota_svc_uuid_wx.u,
        .characteristics = (struct ble_gatt_chr_def[]) {
            {
                // OTA Control Characteristic
                .uuid = &gatt_ota_control_chr_uuid.u,
                .access_cb = ble_ota_control_chr_access_cb,
                .flags = BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_READ | BLE_GATT_CHR_F_NOTIFY,
                .val_handle = &ota_control_chr_handle,
            },
            {
                // OTA Data Characteristic
                .uuid = &gatt_ota_data_chr_uuid.u,
                .access_cb = ble_ota_data_chr_access_cb,
                .flags = BLE_GATT_CHR_F_WRITE | BLE_GATT_CHR_F_READ,
                .val_handle = &ota_data_chr_handle,
            },
            {
                0, // No more characteristics
            }
        },
    },
    {
        0, // No more services
    },
};

// OTA control characteristic access callback
static int ble_ota_control_chr_access_cb(uint16_t conn_handle, uint16_t attr_handle,
                                        struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    int rc = 0;
    uint8_t command;

    switch (ctxt->op) {
        case BLE_GATT_ACCESS_OP_READ_CHR:
            ESP_LOGI(TAG, "OTA Control read");
            // Return current OTA state
            command = ota_in_progress ? 1 : 0;
            rc = os_mbuf_append(ctxt->om, &command, sizeof(command));
            return rc == 0 ? 0 : BLE_ATT_ERR_INSUFFICIENT_RES;

        case BLE_GATT_ACCESS_OP_WRITE_CHR:
            // Process OTA control command
            rc = os_mbuf_copydata(ctxt->om, 0, sizeof(command), &command);
            if (rc != 0) {
                ESP_LOGE(TAG, "Failed to copy OTA command data: %d", rc);
                return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
            }

            ESP_LOGI(TAG, "OTA Control command received: 0x%02x", command);

            // 检查是否是配置服务的命令 (0xB5)
            if (command == 0xB5) {
                ESP_LOGI(TAG, "检测到配置服务命令 0xB5，但被 OTA 服务捕获");
                ESP_LOGI(TAG, "这可能是因为特征值 UUID 冲突或微信小程序使用了错误的 UUID");

                // 我们不处理这个命令，但也不返回错误
                // 这样微信小程序不会收到错误响应
                return 0;
            }

            switch (command) {
                case OTA_CONTROL_START:
                    ESP_LOGI(TAG, "处理OTA开始命令 (0x01)");
                    if (ble_ota_start() == ESP_OK) {
                        ESP_LOGI(TAG, "OTA开始成功，发送成功响应");
                        ble_ota_send_response(conn_handle, OTA_RESP_SUCCESS);
                    } else {
                        ESP_LOGE(TAG, "OTA开始失败，发送错误响应");
                        ble_ota_send_response(conn_handle, OTA_RESP_ERR_INVALID_STATE);
                    }
                    break;

                case OTA_CONTROL_END:
                    ESP_LOGI(TAG, "处理OTA结束命令 (0x02)");
                    if (ble_ota_end() == ESP_OK) {
                        ESP_LOGI(TAG, "OTA结束成功，发送成功响应");
                        ble_ota_send_response(conn_handle, OTA_RESP_SUCCESS);
                    } else {
                        ESP_LOGE(TAG, "OTA结束失败，发送错误响应");
                        ble_ota_send_response(conn_handle, OTA_RESP_ERR_INVALID_STATE);
                    }
                    break;

                case OTA_CONTROL_ABORT:
                    ESP_LOGI(TAG, "处理OTA中止命令 (0x03)");
                    if (ble_ota_abort() == ESP_OK) {
                        ESP_LOGI(TAG, "OTA中止成功，发送成功响应");
                        ble_ota_send_response(conn_handle, OTA_RESP_SUCCESS);
                    } else {
                        ESP_LOGE(TAG, "OTA中止失败，发送错误响应");
                        ble_ota_send_response(conn_handle, OTA_RESP_ERR_INVALID_STATE);
                    }
                    break;

                case OTA_CONTROL_REBOOT:
                    ESP_LOGI(TAG, "处理OTA重启命令 (0x04)");
                    ESP_LOGI(TAG, "发送成功响应并准备重启");
                    ble_ota_send_response(conn_handle, OTA_RESP_SUCCESS);
                    // Delay reboot to allow response to be sent
                    vTaskDelay(pdMS_TO_TICKS(1000));
                    ble_ota_reboot();
                    break;

                default:
                    ESP_LOGW(TAG, "收到未知OTA命令: 0x%02x", command);
                    ble_ota_send_response(conn_handle, OTA_RESP_ERR_INVALID_CMD);
                    break;
            }

            return 0;

        default:
            ESP_LOGE(TAG, "未知的OTA控制操作: %d", ctxt->op);
            return BLE_ATT_ERR_UNLIKELY;
    }
}

// OTA data characteristic access callback
static int ble_ota_data_chr_access_cb(uint16_t conn_handle, uint16_t attr_handle,
                                     struct ble_gatt_access_ctxt *ctxt, void *arg)
{
    int rc = 0;
    esp_err_t err;

    switch (ctxt->op) {
        case BLE_GATT_ACCESS_OP_READ_CHR:
            ESP_LOGI(TAG, "OTA Data read");
            // Return empty data or status
            rc = os_mbuf_append(ctxt->om, ota_data_buffer, ota_data_buffer_size);
            return rc == 0 ? 0 : BLE_ATT_ERR_INSUFFICIENT_RES;

        case BLE_GATT_ACCESS_OP_WRITE_CHR:
            // Process OTA data
            if (!ota_in_progress) {
                ESP_LOGE(TAG, "OTA not in progress, cannot write data");
                return BLE_ATT_ERR_UNLIKELY;
            }

            // Take semaphore
            if (xSemaphoreTake(ota_semaphore, pdMS_TO_TICKS(1000)) != pdTRUE) {
                ESP_LOGE(TAG, "Failed to take OTA semaphore for data write");
                return BLE_ATT_ERR_UNLIKELY;
            }

            // Get data length
            uint16_t data_len = OS_MBUF_PKTLEN(ctxt->om);
            ESP_LOGI(TAG, "收到OTA数据: %d 字节", data_len);

            // Copy data to buffer
            rc = os_mbuf_copydata(ctxt->om, 0, data_len, ota_data_buffer);
            if (rc != 0) {
                ESP_LOGE(TAG, "Failed to copy OTA data: %d", rc);
                xSemaphoreGive(ota_semaphore);
                return BLE_ATT_ERR_INVALID_ATTR_VALUE_LEN;
            }

            // Log first few bytes for debugging
            if (data_len > 0) {
                ESP_LOGI(TAG, "数据前4字节: 0x%02x 0x%02x 0x%02x 0x%02x...",
                         data_len > 0 ? ota_data_buffer[0] : 0,
                         data_len > 1 ? ota_data_buffer[1] : 0,
                         data_len > 2 ? ota_data_buffer[2] : 0,
                         data_len > 3 ? ota_data_buffer[3] : 0);
            }

            // Write data to flash
            err = esp_ota_write(ota_handle, ota_data_buffer, data_len);
            if (err != ESP_OK) {
                ESP_LOGE(TAG, "OTA写入失败: %s", esp_err_to_name(err));
                xSemaphoreGive(ota_semaphore);
                return BLE_ATT_ERR_UNLIKELY;
            }

            // Give semaphore
            xSemaphoreGive(ota_semaphore);

            ESP_LOGI(TAG, "OTA数据写入成功");
            return 0;

        default:
            ESP_LOGE(TAG, "未知的OTA数据操作: %d", ctxt->op);
            return BLE_ATT_ERR_UNLIKELY;
    }
}

// Send OTA response
static void ble_ota_send_response(uint16_t conn_handle, ota_response_code_t code)
{
    uint8_t response[2] = {OTA_CONTROL_RESPONSE, code};
    struct os_mbuf *om;

    ESP_LOGI(TAG, "发送OTA响应: [0x%02x, 0x%02x]", response[0], response[1]);

    om = ble_hs_mbuf_from_flat(response, sizeof(response));
    if (om == NULL) {
        ESP_LOGE(TAG, "Failed to allocate memory for OTA response");
        return;
    }

    int rc = ble_gattc_notify_custom(conn_handle, ota_control_chr_handle, om);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to send OTA response: %d", rc);
    } else {
        ESP_LOGI(TAG, "OTA响应发送成功");
    }
}

// Start OTA process
static esp_err_t ble_ota_start(void)
{
    esp_err_t err;

    ESP_LOGI(TAG, "收到OTA开始命令");

    // Check if OTA is already in progress
    if (ota_in_progress) {
        ESP_LOGE(TAG, "OTA已经在进行中，无法再次开始");
        return ESP_ERR_INVALID_STATE;
    }

    // Take semaphore
    if (xSemaphoreTake(ota_semaphore, pdMS_TO_TICKS(1000)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to take OTA semaphore");
        return ESP_ERR_TIMEOUT;
    }

    // 获取当前运行的分区信息
    const esp_partition_t *running = esp_ota_get_running_partition();
    if (running == NULL) {
        ESP_LOGE(TAG, "Failed to get running partition");
        xSemaphoreGive(ota_semaphore);
        return ESP_ERR_NOT_FOUND;
    }
    ESP_LOGI(TAG, "Running partition: type=%d, subtype=%d, offset=0x%x, label=%s",
             running->type, running->subtype, running->address, running->label);

    // 获取下一个可用的OTA分区
    update_partition = esp_ota_get_next_update_partition(NULL);
    if (update_partition == NULL) {
        ESP_LOGE(TAG, "Failed to get update partition - check partition table");
        ESP_LOGE(TAG, "Make sure you have configured OTA partitions in partitions.csv");
        xSemaphoreGive(ota_semaphore);
        return ESP_ERR_NOT_FOUND;
    }

    ESP_LOGI(TAG, "Writing to partition: type=%d, subtype=%d, offset=0x%x, label=%s",
             update_partition->type, update_partition->subtype, update_partition->address, update_partition->label);

    // 检查分区大小是否足够
    ESP_LOGI(TAG, "Partition size: %d bytes", update_partition->size);
    if (update_partition->size < 0x100000) { // 至少1MB
        ESP_LOGW(TAG, "Update partition size may be too small for firmware");
    }

    // Begin OTA
    err = esp_ota_begin(update_partition, OTA_SIZE_UNKNOWN, &ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to begin OTA: %s", esp_err_to_name(err));
        xSemaphoreGive(ota_semaphore);
        return err;
    }

    // Set OTA in progress flag
    ota_in_progress = true;

    // Give semaphore
    xSemaphoreGive(ota_semaphore);

    ESP_LOGI(TAG, "OTA started successfully");
    return ESP_OK;
}

// End OTA process
static esp_err_t ble_ota_end(void)
{
    esp_err_t err;

    ESP_LOGI(TAG, "收到OTA结束命令");

    // Check if OTA is in progress
    if (!ota_in_progress) {
        ESP_LOGE(TAG, "OTA未在进行中，无法结束");
        return ESP_ERR_INVALID_STATE;
    }

    // Take semaphore
    if (xSemaphoreTake(ota_semaphore, pdMS_TO_TICKS(1000)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to take OTA semaphore for ending OTA");
        return ESP_ERR_TIMEOUT;
    }

    // End OTA
    ESP_LOGI(TAG, "正在结束OTA过程...");
    err = esp_ota_end(ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "结束OTA失败: %s", esp_err_to_name(err));
        xSemaphoreGive(ota_semaphore);
        return err;
    }

    // Set boot partition
    ESP_LOGI(TAG, "正在设置启动分区为: %s", update_partition->label);

    // 检查OTA数据分区是否存在
    const esp_partition_t *otadata_partition = esp_partition_find_first(
        ESP_PARTITION_TYPE_DATA, ESP_PARTITION_SUBTYPE_DATA_OTA, NULL);

    if (otadata_partition == NULL) {
        ESP_LOGE(TAG, "未找到OTA数据分区，请检查分区表配置");
        ESP_LOGI(TAG, "尝试使用备选方法设置启动分区...");
    } else {
        ESP_LOGI(TAG, "找到OTA数据分区: type=%d, subtype=%d, address=0x%x, label=%s",
                 otadata_partition->type, otadata_partition->subtype,
                 otadata_partition->address, otadata_partition->label);
    }

    // 尝试设置启动分区
    err = esp_ota_set_boot_partition(update_partition);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "设置启动分区失败: %s", esp_err_to_name(err));

        // 如果是ESP_ERR_NOT_FOUND错误，尝试初始化OTA数据
        if (err == ESP_ERR_NOT_FOUND && otadata_partition != NULL) {
            ESP_LOGI(TAG, "尝试初始化OTA数据分区...");

            // 创建一个临时缓冲区用于初始化OTA数据
            uint8_t *temp_buf = malloc(otadata_partition->size);
            if (temp_buf != NULL) {
                // 清零缓冲区
                memset(temp_buf, 0xFF, otadata_partition->size);

                // 尝试擦除并写入OTA数据分区
                err = esp_partition_erase_range(otadata_partition, 0, otadata_partition->size);
                if (err == ESP_OK) {
                    ESP_LOGI(TAG, "OTA数据分区擦除成功");

                    // 再次尝试设置启动分区
                    err = esp_ota_set_boot_partition(update_partition);
                    if (err != ESP_OK) {
                        ESP_LOGE(TAG, "再次尝试设置启动分区失败: %s", esp_err_to_name(err));
                    } else {
                        ESP_LOGI(TAG, "成功设置启动分区为: %s", update_partition->label);
                    }
                } else {
                    ESP_LOGE(TAG, "擦除OTA数据分区失败: %s", esp_err_to_name(err));
                }

                // 释放临时缓冲区
                free(temp_buf);
            } else {
                ESP_LOGE(TAG, "内存分配失败，无法初始化OTA数据分区");
            }
        }

        if (err != ESP_OK) {
            xSemaphoreGive(ota_semaphore);
            return err;
        }
    }

    // Reset OTA in progress flag
    ota_in_progress = false;

    // Give semaphore
    xSemaphoreGive(ota_semaphore);

    ESP_LOGI(TAG, "OTA结束成功，下次启动将使用新固件");
    return ESP_OK;
}

// Abort OTA process
static esp_err_t ble_ota_abort(void)
{
    esp_err_t err;

    ESP_LOGI(TAG, "收到OTA中止命令");

    // Check if OTA is in progress
    if (!ota_in_progress) {
        ESP_LOGE(TAG, "OTA未在进行中，无法中止");
        return ESP_ERR_INVALID_STATE;
    }

    // Take semaphore
    if (xSemaphoreTake(ota_semaphore, pdMS_TO_TICKS(1000)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to take OTA semaphore for aborting OTA");
        return ESP_ERR_TIMEOUT;
    }

    // Abort OTA
    ESP_LOGI(TAG, "正在中止OTA过程...");
    err = esp_ota_abort(ota_handle);
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "中止OTA失败: %s", esp_err_to_name(err));
        xSemaphoreGive(ota_semaphore);
        return err;
    }

    // Reset OTA in progress flag
    ota_in_progress = false;

    // Give semaphore
    xSemaphoreGive(ota_semaphore);

    ESP_LOGI(TAG, "OTA已成功中止");
    return ESP_OK;
}

// Reboot device
static void ble_ota_reboot(void)
{
    ESP_LOGI(TAG, "收到重启命令，3秒后重启设备...");

    // 打印当前和下一个启动分区信息
    const esp_partition_t *running = esp_ota_get_running_partition();
    const esp_partition_t *boot = esp_ota_get_boot_partition();

    if (running != NULL) {
        ESP_LOGI(TAG, "当前运行分区: %s (0x%x)", running->label, running->address);
    }

    if (boot != NULL) {
        ESP_LOGI(TAG, "下次启动分区: %s (0x%x)", boot->label, boot->address);
        if (boot != running) {
            ESP_LOGI(TAG, "重启后将使用新固件!");
        }
    }

    // 延迟3秒，确保所有日志都已打印并且BLE响应已发送
    vTaskDelay(pdMS_TO_TICKS(3000));

    ESP_LOGI(TAG, "正在重启...");
    esp_restart();
}

// OTA service callback for GATT registration
void ble_ota_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg)
{
    char buf[BLE_UUID_STR_LEN];

    switch (ctxt->op) {
        case BLE_GATT_REGISTER_OP_SVC:
            if (ble_uuid_cmp(ctxt->svc.svc_def->uuid, &gatt_ota_svc_uuid_wx.u) == 0) {
                ota_svc_handle = ctxt->svc.handle;
                ESP_LOGI(TAG, "OTA service registered: handle=%d, uuid=%s",
                         ota_svc_handle, ble_uuid_to_str(ctxt->svc.svc_def->uuid, buf));
            }
            break;

        case BLE_GATT_REGISTER_OP_CHR:
            if (ble_uuid_cmp(ctxt->chr.chr_def->uuid, &gatt_ota_control_chr_uuid.u) == 0) {
                ESP_LOGI(TAG, "OTA control characteristic registered: def_handle=%d, val_handle=%d, uuid=%s",
                         ctxt->chr.def_handle, ctxt->chr.val_handle, ble_uuid_to_str(ctxt->chr.chr_def->uuid, buf));
            } else if (ble_uuid_cmp(ctxt->chr.chr_def->uuid, &gatt_ota_data_chr_uuid.u) == 0) {
                ESP_LOGI(TAG, "OTA data characteristic registered: def_handle=%d, val_handle=%d, uuid=%s",
                         ctxt->chr.def_handle, ctxt->chr.val_handle, ble_uuid_to_str(ctxt->chr.chr_def->uuid, buf));
            }
            break;

        case BLE_GATT_REGISTER_OP_DSC:
            ESP_LOGI(TAG, "OTA descriptor registered: handle=%d, uuid=%s",
                     ctxt->dsc.handle, ble_uuid_to_str(ctxt->dsc.dsc_def->uuid, buf));
            break;

        default:
            break;
    }
}

// Get OTA state
bool ble_ota_is_in_progress(void)
{
    return ota_in_progress;
}

// Initialize OTA service
esp_err_t ble_ota_service_init(void)
{
    int rc;

    // 检查分区表配置
    const esp_partition_t *running = esp_ota_get_running_partition();
    if (running == NULL) {
        ESP_LOGE(TAG, "获取当前运行分区失败");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "当前运行分区: type=%d, subtype=%d, offset=0x%x, label=%s",
             running->type, running->subtype, running->address, running->label);

    // 检查是否有可用的OTA分区
    const esp_partition_t *update_part = esp_ota_get_next_update_partition(NULL);
    if (update_part == NULL) {
        ESP_LOGE(TAG, "未找到OTA分区！请检查分区表配置！");
        ESP_LOGE(TAG, "没有正确的分区表配置，OTA功能将无法工作");
        // 我们不在这里返回错误，因为我们希望应用程序仍然能够运行，即使OTA可能不工作
        // 但我们会记录警告
    } else {
        ESP_LOGI(TAG, "可用的OTA分区: type=%d, subtype=%d, offset=0x%x, label=%s",
                 update_part->type, update_part->subtype, update_part->address, update_part->label);
    }

    // 检查OTA数据分区
    const esp_partition_t *otadata_part = esp_partition_find_first(
        ESP_PARTITION_TYPE_DATA, ESP_PARTITION_SUBTYPE_DATA_OTA, NULL);
    if (otadata_part == NULL) {
        ESP_LOGW(TAG, "未找到OTA数据分区！OTA功能可能无法正常工作");
        ESP_LOGW(TAG, "请确保分区表中包含类型为data，子类型为ota的分区");
    } else {
        ESP_LOGI(TAG, "找到OTA数据分区: type=%d, subtype=%d, offset=0x%x, size=%d, label=%s",
                 otadata_part->type, otadata_part->subtype, otadata_part->address,
                 otadata_part->size, otadata_part->label);
    }

    // 打印当前启动分区信息
    const esp_partition_t *boot_part = esp_ota_get_boot_partition();
    if (boot_part != NULL) {
        ESP_LOGI(TAG, "当前启动分区: type=%d, subtype=%d, offset=0x%x, label=%s",
                 boot_part->type, boot_part->subtype, boot_part->address, boot_part->label);

        if (boot_part != running) {
            ESP_LOGW(TAG, "当前运行分区与启动分区不一致！");
            ESP_LOGW(TAG, "这可能是因为OTA更新后未重启设备");
        }
    } else {
        ESP_LOGW(TAG, "无法获取启动分区信息");
    }


    // 设置NimBLE的MTU大小 - 增加MTU以支持更大的数据包
    // 对于NimBLE，我们使用ble_att_set_preferred_mtu函数
    ble_att_set_preferred_mtu(512);
    ESP_LOGI(TAG, "设置BLE首选MTU大小为512字节");

    // Create OTA semaphore
    ota_semaphore = xSemaphoreCreateMutex();
    if (ota_semaphore == NULL) {
        ESP_LOGE(TAG, "Failed to create OTA semaphore");
        return ESP_ERR_NO_MEM;
    }

    // Register OTA service
    rc = ble_gatts_count_cfg(ota_service_defs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to count GATT service config: %d", rc);
        return ESP_FAIL;
    }

    rc = ble_gatts_add_svcs(ota_service_defs);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to add GATT service: %d", rc);
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "OTA service initialized");
    return ESP_OK;
}
