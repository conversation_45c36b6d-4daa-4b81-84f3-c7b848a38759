/*
 * bsp_esp32_iic.h
 *
 *  Created on: 2023年9月5日
 *      Author: KB402
 */

#ifndef PLATFORM_INC_BSP_ESP32_IIC_H_
#define PLATFORM_INC_BSP_ESP32_IIC_H_
#include "driver/i2c.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

typedef enum
{
	I2C_Speed_Config_100k = 100000U,
    I2C_Speed_Config_200k = 200000U,
    I2C_Speed_Config_300k = 300000U,
    I2C_Speed_Config_400k = 400000U,
    I2C_Speed_Config_1M = 1000000U,
}I2C_Speed_Config;

void bsp_iic_master_init(I2C_Speed_Config clk_speed);
int bsp_iic_write(uint16_t dev_addr, uint8_t reg_addr, uint16_t data);
int bsp_iic_read(uint16_t devAddr, uint8_t regAddr, uint16_t *regVal);

#endif /* PLATFORM_INC_BSP_ESP32_IIC_H_ */
