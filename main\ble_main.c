/**
 * @file ble_main.c
 * @brief BLE Main Implementation
 * @version 1.0
 * @date 2024-08-01
 */

#include <stdio.h>
#include <string.h>
#include "esp_log.h"
#include "nvs_flash.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_system.h"  // For esp_read_mac

/* BLE */
#include "nimble/nimble_port.h"
#include "nimble/nimble_port_freertos.h"
#include "host/ble_hs.h"
#include "host/util/util.h"
#include "services/gap/ble_svc_gap.h"
#include "services/gatt/ble_svc_gatt.h"
#include "esp_nimble_hci.h"

#include "ble_main.h"
#include "ble_ota_service.h"
#include "ble_config_service.h"
#include "ble_monitoring_service.h"

static const char *TAG = "BLE_MAIN";

// Device name
#define DEVICE_NAME "Multi-Sensor-"

// BLE connection state
static bool is_ble_connected = false;

// Own address type
static uint8_t own_addr_type;

// Forward declarations
static void ble_on_sync(void);
static void ble_on_reset(int reason);
static void ble_host_task(void *param);
static int ble_gap_event(struct ble_gap_event *event, void *arg);

// Print BLE address
static void print_addr(const uint8_t *addr)
{
    for (int i = 5; i >= 0; i--) {
        ESP_LOGI(TAG, "%02x%s", addr[i], (i > 0) ? ":" : "");
    }
}

// Start advertising
static void ble_advertise(void)
{
    struct ble_gap_adv_params adv_params;
    struct ble_hs_adv_fields fields;
    int rc;

    // Set advertisement data
    memset(&fields, 0, sizeof(fields));

    // Flags
    fields.flags = BLE_HS_ADV_F_DISC_GEN | BLE_HS_ADV_F_BREDR_UNSUP;

    // Include TX power level
    fields.tx_pwr_lvl_is_present = 1;
    fields.tx_pwr_lvl = BLE_HS_ADV_TX_PWR_LVL_AUTO;

    // Include device name
    const char *name = ble_svc_gap_device_name();
    fields.name = (uint8_t *)name;
    fields.name_len = strlen(name);
    fields.name_is_complete = 1;

    rc = ble_gap_adv_set_fields(&fields);
    if (rc != 0) {
        ESP_LOGE(TAG, "Error setting advertisement data: %d", rc);
        return;
    }

    // Begin advertising
    memset(&adv_params, 0, sizeof(adv_params));
    adv_params.conn_mode = BLE_GAP_CONN_MODE_UND;
    adv_params.disc_mode = BLE_GAP_DISC_MODE_GEN;

    rc = ble_gap_adv_start(own_addr_type, NULL, BLE_HS_FOREVER, &adv_params, ble_gap_event, NULL);
    if (rc != 0) {
        ESP_LOGE(TAG, "Error starting advertisement: %d", rc);
        return;
    }

    ESP_LOGI(TAG, "BLE advertising started");
}

// GAP event handler
static int ble_gap_event(struct ble_gap_event *event, void *arg)
{
    struct ble_gap_conn_desc desc;
    int rc;

    switch (event->type) {
        case BLE_GAP_EVENT_CONNECT:
            // A new connection was established or a connection attempt failed
            if (event->connect.status == 0) {
                // Connection established
                is_ble_connected = true;
                rc = ble_gap_conn_find(event->connect.conn_handle, &desc);
                assert(rc == 0);
                ESP_LOGI(TAG, "Connection established (handle=%d)", event->connect.conn_handle);

                // 连接后主动请求更新MTU大小
                ESP_LOGI(TAG, "正在请求更新MTU大小为512...");
                rc = ble_gattc_exchange_mtu(event->connect.conn_handle, NULL, NULL);
                if (rc != 0) {
                    ESP_LOGW(TAG, "MTU交换请求失败: %d", rc);
                }

                // 设置连接参数以优化数据传输
                struct ble_gap_upd_params conn_params;
                memset(&conn_params, 0, sizeof(conn_params));

                // 设置较小的连接间隔以提高传输速度 (单位: 1.25ms)
                // 最小连接间隔: 7.5ms (6*1.25ms)
                // 最大连接间隔: 15ms (12*1.25ms)
                conn_params.itvl_min = 6;    // 7.5ms
                conn_params.itvl_max = 12;   // 15ms

                // 设置从设备延迟 (单位: 连接事件)
                conn_params.latency = 0;     // 无延迟

                // 设置监督超时 (单位: 10ms)
                // 至少是 (1 + latency) * 2 * max_interval
                conn_params.supervision_timeout = 100; // 1000ms

                // 最小连接事件长度和最大连接事件长度 (单位: 0.625ms)
                conn_params.min_ce_len = 0;
                conn_params.max_ce_len = 0;

                ESP_LOGI(TAG, "正在更新连接参数: 间隔=%.2f-%.2fms, 延迟=%d, 超时=%dms",
                         conn_params.itvl_min * 1.25, conn_params.itvl_max * 1.25,
                         conn_params.latency, conn_params.supervision_timeout * 10);

                rc = ble_gap_update_params(event->connect.conn_handle, &conn_params);
                if (rc != 0) {
                    ESP_LOGW(TAG, "连接参数更新请求失败: %d", rc);
                }
            } else {
                // Connection failed; resume advertising
                is_ble_connected = false;
                ESP_LOGI(TAG, "Connection failed (status=%d)", event->connect.status);
                ble_advertise();
            }
            return 0;

        case BLE_GAP_EVENT_DISCONNECT:
            // Connection terminated; resume advertising
            is_ble_connected = false;
            ESP_LOGI(TAG, "Disconnected (reason=%d)", event->disconnect.reason);
            ble_advertise();
            return 0;

        case BLE_GAP_EVENT_ADV_COMPLETE:
            // Advertising completed; restart advertising
            ESP_LOGI(TAG, "Advertising complete");
            ble_advertise();
            return 0;

        case BLE_GAP_EVENT_SUBSCRIBE:
            ESP_LOGI(TAG, "Subscribe event; conn_handle=%d attr_handle=%d "
                     "reason=%d prevn=%d curn=%d previ=%d curi=%d",
                     event->subscribe.conn_handle,
                     event->subscribe.attr_handle,
                     event->subscribe.reason,
                     event->subscribe.prev_notify,
                     event->subscribe.cur_notify,
                     event->subscribe.prev_indicate,
                     event->subscribe.cur_indicate);
            return 0;

        case BLE_GAP_EVENT_MTU:
            ESP_LOGI(TAG, "MTU更新事件: 连接句柄=%d 协商后的MTU=%d",
                     event->mtu.conn_handle,
                     event->mtu.value);

            if (event->mtu.value < 512) {
                ESP_LOGW(TAG, "协商的MTU小于首选值512，这可能会影响OTA传输速度");
                ESP_LOGW(TAG, "请确保微信小程序也请求了较大的MTU值");
            } else {
                ESP_LOGI(TAG, "MTU协商成功，使用较大的MTU值将提高OTA传输速度");
            }
            return 0;

        case BLE_GAP_EVENT_CONN_UPDATE:
            // 连接参数更新事件
            if (event->conn_update.status == 0) {
                // 连接参数更新成功
                rc = ble_gap_conn_find(event->conn_update.conn_handle, &desc);
                if (rc == 0) {
                    ESP_LOGI(TAG, "连接参数更新成功: 间隔=%.2fms, 延迟=%d, 超时=%dms",
                             desc.conn_itvl * 1.25,
                             desc.conn_latency,
                             desc.supervision_timeout * 10);
                }
            } else {
                ESP_LOGW(TAG, "连接参数更新失败: %d", event->conn_update.status);
            }
            return 0;

        case BLE_GAP_EVENT_CONN_UPDATE_REQ:
            // 对方请求更新连接参数
            ESP_LOGI(TAG, "收到连接参数更新请求");
            // 我们可以在这里接受或拒绝请求
            // 默认情况下，NimBLE会自动接受请求
            return 0;

        default:
            return 0;
    }
}

// Combined GATT service registration callback
void ble_gatt_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg)
{
    // Route to the appropriate service-specific callback
    ble_ota_register_cb(ctxt, arg);
    ble_config_register_cb(ctxt, arg);
    ble_monitoring_register_cb(ctxt, arg);
}

// BLE reset callback
static void ble_on_reset(int reason)
{
    ESP_LOGE(TAG, "BLE stack reset, reason=%d", reason);
}

// BLE sync callback
static void ble_on_sync(void)
{
    int rc;

    // Determine address to use while advertising
    rc = ble_hs_id_infer_auto(0, &own_addr_type);
    if (rc != 0) {
        ESP_LOGE(TAG, "Error determining address type: %d", rc);
        return;
    }

    // Print device address
    uint8_t addr_val[6] = {0};
    rc = ble_hs_id_copy_addr(own_addr_type, addr_val, NULL);
    ESP_LOGI(TAG, "Device Address: ");
    print_addr(addr_val);

    // Begin advertising
    ble_advertise();
}

// BLE host task
static void ble_host_task(void *param)
{
    ESP_LOGI(TAG, "BLE Host Task Started");

    // This function will return only when nimble_port_stop() is executed
    nimble_port_run();

    // If we reach here, the NimBLE host has stopped
    ESP_LOGI(TAG, "BLE Host Task Stopped");

    // Cleanup
    nimble_port_freertos_deinit();
}

// Cleanup function for BLE (call this when shutting down)
void ble_main_deinit(void)
{
    ESP_LOGI(TAG, "Deinitializing BLE stack...");

    // Stop the NimBLE host task
    nimble_port_stop();

    // Deinitialize NimBLE
    nimble_port_deinit();

    // Deinitialize controller
    esp_nimble_hci_and_controller_deinit();

    ESP_LOGI(TAG, "BLE stack deinitialized");
}

// Initialize BLE
esp_err_t ble_main_init(void)
{
    int rc;

    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // Initialize BLE controller
    ESP_LOGI(TAG, "Initializing BLE controller...");
    esp_err_t err = esp_nimble_hci_and_controller_init();
    if (err != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize BLE controller: %d", err);
        return err;
    }

    // Initialize NimBLE
    ESP_LOGI(TAG, "Initializing NimBLE stack...");
    nimble_port_init();

    // Initialize the NimBLE host configuration
    ble_hs_cfg.reset_cb = ble_on_reset;
    ble_hs_cfg.sync_cb = ble_on_sync;
    ble_hs_cfg.gatts_register_cb = ble_gatt_register_cb;
    ble_hs_cfg.store_status_cb = ble_store_util_status_rr;

    // Initialize services
    ble_svc_gap_init();
    ble_svc_gatt_init();


    // Initialize configuration service
    rc = ble_config_service_init();
    if (rc != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize configuration service: %d", rc);
        return ESP_FAIL;
    }

    // Initialize monitoring service
    rc = ble_monitoring_service_init();
    if (rc != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize monitoring service: %d", rc);
        return ESP_FAIL;
    }

    // Initialize OTA service
    rc = ble_ota_service_init();
    if (rc != ESP_OK) {
        ESP_LOGE(TAG, "Failed to initialize OTA service: %d", rc);
        return ESP_FAIL;
    }

    // Get MAC address for unique device name
    uint8_t mac[6];
    ret = esp_read_mac(mac, ESP_MAC_WIFI_STA);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to get MAC address: %d", ret);
        return ESP_FAIL;
    }

    // Create a unique device name with MAC address suffix
    char device_name[32];
    snprintf(device_name, sizeof(device_name), "%s%02X%02X", DEVICE_NAME, mac[4], mac[5]);
    ESP_LOGI(TAG, "Setting device name: %s", device_name);

    // Set the device name with unique suffix
    rc = ble_svc_gap_device_name_set(device_name);
    if (rc != 0) {
        ESP_LOGE(TAG, "Failed to set device name: %d", rc);
        return ESP_FAIL;
    }

    // Start the NimBLE host task
    nimble_port_freertos_init(ble_host_task);

    ESP_LOGI(TAG, "BLE stack initialized");
    return ESP_OK;
}
