# BLE OTA 功能使用说明

本项目实现了通过蓝牙低功耗(BLE)进行固件空中升级(OTA)的功能。

## 分区表配置

OTA功能需要正确配置分区表才能工作。项目使用了自定义分区表`partitions.csv`，该分区表包含以下分区：

```
# Name,   Type, SubType, Offset,  Size, Flags
nvs,      data, nvs,     0x9000,  0x6000,
phy_init, data, phy,     0xf000,  0x1000,
factory,  app,  factory, 0x10000, 1M,
ota_0,    app,  ota_0,   ,        1M,
ota_1,    app,  ota_1,   ,        1M,
```

这个分区表定义了：
- `factory`: 出厂固件分区
- `ota_0`和`ota_1`: 两个OTA应用分区，用于OTA更新

## BLE OTA服务

BLE OTA服务包含两个特征：

1. **OTA控制特征** (UUID: E1FF4263-DE45-A088-CCB0-DA45F02E4742)
   - 用于发送控制命令，如开始OTA、结束OTA、中止OTA和重启设备
   - 支持读取、写入和通知

2. **OTA数据特征** (UUID: E2FF4263-DE45-A088-CCB0-DA45F02E4742)
   - 用于传输固件数据
   - 支持读取和写入

## OTA更新流程

1. **开始OTA**:
   - 客户端连接到设备
   - 客户端向OTA控制特征写入`0x01`(OTA_CONTROL_START)命令
   - 设备准备OTA更新分区并响应成功代码

2. **固件传输**:
   - 客户端将固件数据分块写入OTA数据特征
   - 设备将数据写入OTA更新分区

3. **结束OTA**:
   - 所有数据传输完成后，客户端向OTA控制特征写入`0x02`(OTA_CONTROL_END)命令
   - 设备完成OTA更新并设置新分区为启动分区
   - 设备响应成功代码

4. **重启**:
   - 客户端向OTA控制特征写入`0x04`(OTA_CONTROL_REBOOT)命令
   - 设备重启并开始运行新固件

## 客户端实现

要执行OTA更新，您需要一个能够：
1. 通过BLE连接到设备
2. 发现OTA服务和特征
3. 发送OTA控制命令
4. 传输固件数据

的客户端应用程序。这可以是移动应用、桌面应用或另一个ESP32设备。

## 测试OTA功能

1. 构建并烧录此固件到您的设备
2. 使用BLE扫描应用验证设备正在以"Human Detector OTA"的名称广播
3. 开发或使用支持OTA协议的BLE客户端
4. 使用新固件执行OTA更新

## 错误处理

OTA服务包含以下错误处理机制：
- 检查分区表配置
- 验证OTA状态
- 使用互斥量保护OTA操作
- 响应代码通知客户端操作结果

## 安全考虑

当前实现没有包含身份验证或加密机制。在生产环境中，建议添加：
- BLE连接加密
- OTA更新身份验证
- 固件签名验证

## 调试信息

OTA服务会输出详细的日志信息，包括：
- 分区信息
- OTA状态变化
- 错误和警告

这些日志可以帮助诊断OTA过程中的问题。
