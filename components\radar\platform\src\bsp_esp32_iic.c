/*
 * bsp_esp32_iic.c
 *
 *  Created on: 2023年9月5日
 *      Author: KB402
 */


/* About Pin:
*  if one pin % 2 is 0 ===> this pin can be used as i2c scl function
*  if one pin % 2 is 1 ===> this pin can be used as i2c sda function
*  such as: GLB_GPIO_PIN_0 ===> scl
*           GLB_GPIO_PIN_1 ===> sda
*/
#include "bsp_esp32_iic.h"

#define I2C_MASTER_SCL_IO 7        // 定义I2C时钟线的引脚号
#define I2C_MASTER_SDA_IO 6        // 定义I2C数据线的引脚号
#define I2C_MASTER_NUM I2C_NUM_1    // 定义I2C控制器号
#define I2C_MASTER_TIMEOUT_MS       1000


void bsp_iic_master_init(I2C_Speed_Config clk_speed)
{
	static i2c_config_t conf;
    conf.mode = I2C_MODE_MASTER;                // 设置I2C模式为主模式
    conf.sda_io_num = I2C_MASTER_SDA_IO;        // 配置数据线引脚
    conf.sda_pullup_en = GPIO_PULLUP_ENABLE;    // 启用上拉电阻
    conf.scl_io_num = I2C_MASTER_SCL_IO;        // 配置时钟线引脚
    conf.scl_pullup_en = GPIO_PULLUP_ENABLE;    // 启用上拉电阻
    conf.master.clk_speed = clk_speed;             // 设置I2C时钟速度，例如100kHz

    // 安装并初始化I2C驱动
    i2c_param_config(I2C_MASTER_NUM, &conf);
    i2c_driver_install(I2C_MASTER_NUM, conf.mode, 0, 0, 0);
}

// 往寄存器reg_addr写data
int bsp_iic_write(uint16_t dev_addr, uint8_t reg_addr, uint16_t data)
{
    // 需要连续写
	uint8_t ret;
    uint8_t databuff[3];
    databuff[0] = reg_addr;
    databuff[1] = (uint8_t)(data >> 8);
    databuff[2] = (uint8_t)(data);

    dev_addr >>= 1;

    ret = i2c_master_write_to_device(I2C_MASTER_NUM, dev_addr, (const uint8_t *)databuff, sizeof(databuff)/sizeof(databuff[0]), I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    return ret;
}


int bsp_iic_read(uint16_t devAddr, uint8_t regAddr, uint16_t *regVal)
{
    uint16_t val = 0;

    devAddr >>= 1;
    i2c_master_write_read_device(I2C_MASTER_NUM, devAddr, &regAddr, 1,  (uint8_t *)&val, sizeof(val), I2C_MASTER_TIMEOUT_MS / portTICK_PERIOD_MS);
    // 直接用移位操作进行大小端转换
    *regVal = (val >> 8) | (val << 8);

    return 0;
}

