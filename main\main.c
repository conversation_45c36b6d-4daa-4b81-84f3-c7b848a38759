/**
 * @file app_main_example.c
 * @brief S18L262 传感器驱动使用示例
 * @version 1.2 // 版本更新
 * @date 2024-08-15 // 修改日期
 */

#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_log.h"
#include "s18l262.h" // 包含传感器驱动头文件
#include "ble_main.h"
#include "ble_config_service.h" // 包含配置服务头文件
#include "ble_monitoring_service.h" // 包含监测服务头文件
#include "temp_sensor.h" // 包含温度传感器驱动头文件
#include "ltr308.h"

#include "platform.h"
#include "banyan.h"
#include "dataprocess.h"
#include "cmdprocess.h"
#include "radar_config.h"
#include "system.h"
#include "bsp_esp32_uart.h" // 包含UART驱动头文件
static const char *TAG_APP = "ota-2";

volatile bool init_finish = false;
// 用于在中断中设置的标志，表示PIR传感器是否检测到人体
volatile bool pir_human_detected = false;

// 用于跟踪是否已经进入过 RAW_DATA 模式
static bool raw_data_mode_entered = false;
// 用于存储上一次的配置模式
static ble_config_mode_t previous_config_mode = CONFIG_MODE_RAW_DATA;

/**
 * @brief 用户定义的中断处理函数
 *
 * 当PIR传感器触发中断时，此函数将被调用。
 * @param arg 传递给中断处理函数的参数 (在 s18l262_set_intr_handler 中设置)
 */
static void user_pir_interrupt_handler(void* arg)
{
    // 注意：中断处理函数应该尽可能快地执行。
    // 避免在 ISR 中执行耗时操作，如打印过多日志或复杂的计算。
    // 通常的做法是在 ISR 中设置一个标志位，然后在主循环或一个单独的任务中处理该标志。

    // 更新全局标志
    pir_human_detected = s18l262_read_state();

    // 注意：不要在中断处理函数中调用 ble_human_detection_update_status
    // 因为它可能包含耗时操作。状态更新将在主循环中处理。

    // // 使用 ets_printf 在中断中安全地打印（仅用于调试，生产环境应移除）
    // if (current_state) {
    //     ets_printf("PIR ISR: Human DETECTED! Pin state: HIGH\n");
    // } else {
    //     ets_printf("PIR ISR: Human LEFT or signal ended. Pin state: LOW\n");
    // }
}


void app_main(void)
{
    //led初始化
	led_init(&init_finish);

    // 初始化BLE
    ble_main_init();

    // 初始化光照传感器
    ltr308_config_t config = {
        .i2c_addr = LTR308_ADDR,
        .i2c_port = 0,
        .sda_pin = 2,
        .scl_pin = 3,
        .isr_pin = -1     // 40 ，默认不开中断
    };

    ltr308_init(&config);


    s18l262_config_t current_sensor_config = {
        .pir_rel_pin = S18L262_PIR_REL_PIN,
        .dac_set_ontime_pin = S18L262_DAC_SET_ONTIME_PIN,
        .dac_set_sens_pin = S18L262_DAC_SET_SENS_PIN,
        .use_pwm = false,
        .intr_type = S18L262_INTR_ANY
    };    // 修改中断类型为任意边沿，上上升沿表示人运动动作出现，下降沿表示人静止或者离开

    // PWM配置示例，当前未使用
    // s18l262_config_t current_sensor_config = {
    //     .pir_rel_pin = 39,
    //     .dac_set_ontime_pin = 38,
    //     .dac_set_sens_pin = 46,
    //     .use_pwm = true,
    //     .intr_type = S18L262_INTR_ANY
    // };

    s18l262_init(&current_sensor_config);


    if (current_sensor_config.intr_type != S18L262_INTR_NONE) {
        esp_err_t handler_ret = s18l262_set_intr_handler(user_pir_interrupt_handler, (void*)"PIR_EVENT");
        if (handler_ret != ESP_OK) {
            ESP_LOGE(TAG_APP, "设置中断处理函数失败: %s", esp_err_to_name(handler_ret));
        } else {
            ESP_LOGI(TAG_APP, "中断处理函数设置成功。");
        }
    }


    // 用于存储上一次 pir_human_detected 的状态
    static bool previous_detected_state = false;

    // 在循环开始前，读取当前状态并打印
    pir_human_detected = s18l262_read_state(); // 确保全局标志与当前状态一致
    previous_detected_state = pir_human_detected;


    // 记录上次状态变化的时间
    uint32_t last_state_change_time = xTaskGetTickCount();
    uint32_t debounce_time_ms = 2000; // 防抖时间，单位毫秒

    // 初始化温度传感器
    temp_sensor_init();

    // 用于存储温度值的变量
    float chip_temperature = 0.0f;
    uint32_t lux;

    //雷达系统初始化
    Platform_Init();
    Config_Init();
//    Radar_Init();
    Radar_Init_Stop_BanyanE();
    Radar_Init_Start_BanyanE();

    CmdProc_TaskInit();
    DataProc_Init();    // 数据初始化、SPI初始化
    CmdProc_Init();
    // DataProc_TaskInit();
    init_finish = 1;//初始化完成
    // 主循环，处理传感器事件
    while (1) {
        // 获取当前配置模式
        ble_config_mode_t current_mode = ble_config_get_current_mode();
        // 更新附加传感器数据
        ltr308_read_lux(&lux);
        read_chip_temperature(&chip_temperature);
        additional_sensors_data_t sensors_data = {
            .light = (uint16_t)lux,  // 模拟光线传感器数据
            .pir = (uint8_t)pir_human_detected,  // PIR 传感器检测到人
            .temperature = (int16_t)(chip_temperature * 10)  // 实际温度数据
        };
        // 检查模式是否发生变化
        bool mode_changed = (current_mode != previous_config_mode);
        if (mode_changed) {
            ESP_LOGI(TAG_APP, "配置模式从 %d 切换到 %d", previous_config_mode, current_mode);
            // 暂时不更新 previous_config_mode，等处理完模式切换逻辑后再更新
        }

        // 根据当前模式处理数据
        switch (current_mode) {
            case CONFIG_MODE_RAW_DATA:
                // 第一次进入 RAW_DATA 模式或从其他模式切换回 RAW_DATA 模式时执行特殊配置
                if (!raw_data_mode_entered || (mode_changed && previous_config_mode != CONFIG_MODE_RAW_DATA)) {
                    if (!raw_data_mode_entered) {
                        ESP_LOGI(TAG_APP, "首次进入 RAW_DATA 模式，配置高速模式");
                        // 标记已经进入过 RAW_DATA 模式
                        raw_data_mode_entered = true;
                    } else {
                        ESP_LOGI(TAG_APP, "从其他模式切换回 RAW_DATA 模式，重新配置高速模式");
                    }

                    // 设置所有组件的日志级别为 ERROR，减少日志输出
                    esp_log_level_set("*", ESP_LOG_ERROR);
                    ESP_LOGE(TAG_APP, "已切换到高速模式：日志级别设为 ERROR，UART 波特率设为 2000000");
                    //  UART，设置更高的波特率
                    uart_set_baudrate(UART_NUM_0, 2000000); // 设置 UART0 波特率为 2000000

                }

                // 原始数据模式 - 使用实际传感器数据
                // 检查 pir_human_detected 的状态是否发生变化
                if (pir_human_detected != previous_detected_state) {
                    // 检查是否超过防抖时间
                    uint32_t current_time = xTaskGetTickCount();
                    uint32_t elapsed_time = (current_time - last_state_change_time) * portTICK_PERIOD_MS;

                    if (elapsed_time >= debounce_time_ms) {
                        Banyan_ReadTemperature(I2C_ADDR_BanYan_Chip0>>1);
                        Banyan_ReadPower(I2C_ADDR_BanYan_Chip0>>1);
                        ble_monitoring_update_sensors(&sensors_data);
                        // ESP_LOGE(TAG_APP, "主循环：状态变化 - 人体检测状态更新为 %d", pir_human_detected);
                        // 更新上一次的状态和时间戳
                        previous_detected_state = pir_human_detected;
                        last_state_change_time = current_time;
                    }
                }
                break;

            case CONFIG_MODE_VITALS_MONITOR:
            case CONFIG_MODE_FALL_DETECTION:
            case CONFIG_MODE_PRESENCE_DETECTION:
                // 如果之前在 RAW_DATA 模式，现在切换到其他模式，恢复正常设置
                if (raw_data_mode_entered && mode_changed && previous_config_mode == CONFIG_MODE_RAW_DATA) {
                    ESP_LOGE(TAG_APP, "从 RAW_DATA 模式切换到其他模式，恢复正常设置");

                    // 恢复默认日志级别
                    esp_log_level_set("*", ESP_LOG_INFO);

                    // 恢复默认波特率
                    uart_set_baudrate(UART_NUM_0, 115200);

                    ESP_LOGI(TAG_APP, "已恢复正常模式：日志级别设为 INFO，UART 波特率设为 115200");
                }

                // 其他模式 - 使用模拟数据
                ESP_LOGI(TAG_APP, "使用模式 %d 的模拟数据", current_mode);

                ble_monitoring_update_sensors(&sensors_data);
                // 发送模拟数据
                ble_config_send_simulated_data();
                break;

            default:
                // 未知模式，使用默认行为
                ESP_LOGW(TAG_APP, "未知模式 %d", current_mode);
                break;
        }

        // 更新上一次的配置模式
        if (mode_changed) {
            previous_config_mode = current_mode;
        }

        vTaskDelay(pdMS_TO_TICKS(2000)); // 每秒检查一次
    }

    // 通常不会执行到这里，但在某些情况下可能需要反初始化
    // ESP_LOGI(TAG_APP, "示例结束，反初始化 S18L262...");
    // esp_err_t deinit_ret = s18l262_deinit();
    // if (deinit_ret != ESP_OK) {
    //     ESP_LOGE(TAG_APP, "S18L262 反初始化失败: %s", esp_err_to_name(deinit_ret));
    // } else {
    //     ESP_LOGI(TAG_APP, "S18L262 反初始化成功。");
    // }
}
