/**
 * @file ble_main.h
 * @brief BLE Main Functions
 * @version 1.1
 * @date 2024-08-15
 */

#ifndef BLE_MAIN_H
#define BLE_MAIN_H

#include "esp_err.h"
#include "host/ble_hs.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief Combined GATT service registration callback
 *
 * This callback is used to handle registration events for all services.
 * It will route the events to the appropriate service-specific callbacks.
 *
 * @param ctxt GATT registration context
 * @param arg User argument
 */
void ble_gatt_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg);

/**
 * @brief Initialize the BLE stack and start advertising
 *
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_main_init(void);

/**
 * @brief Deinitialize the BLE stack
 *
 * Call this function when shutting down the application
 * to properly clean up BLE resources.
 */
void ble_main_deinit(void);

#ifdef __cplusplus
}
#endif

#endif /* BLE_MAIN_H */
