/**
 * @file s18l262.h
 * @brief S18-L262B-2人体感应传感器驱动
 * @version 0.3 // 版本更新
 * @date 2024-07-28 // 与 .c 文件日期对应
 */

#ifndef S18L262_H
#define S18L262_H

#include <stdint.h>
#include <stdbool.h>
#include "esp_err.h"
#include "driver/gpio.h"
#include "driver/ledc.h"

// 引脚定义 (Pin Definitions)
// 如果在 s18l262_config_t 中为引脚传入负值，则会使用这些宏定义的默认引脚号。
#define S18L262_PIR_REL_PIN         39  // 人体感应输出引脚 (Human presence detection output pin)
#define S18L262_DAC_SET_ONTIME_PIN  38  // 延时时间设置引脚 (Delay time setting pin)
#define S18L262_DAC_SET_SENS_PIN    46  // 灵敏度设置引脚 (Sensitivity setting pin)

// PWM配置 (PWM Configuration)
#define S18L262_PWM_FREQ            1000    // PWM频率 (Hz) (PWM frequency)
#define S18L262_PWM_RESOLUTION      LEDC_TIMER_10_BIT  // PWM分辨率 (PWM resolution, e.g., 10-bit for 0-1023 duty)
#define S18L262_PWM_TIMER           LEDC_TIMER_0       // PWM定时器 (PWM timer to use)
#define S18L262_ONTIME_CHANNEL      LEDC_CHANNEL_0     // 延时时间PWM通道 (PWM channel for ontime setting)
#define S18L262_SENS_CHANNEL        LEDC_CHANNEL_1     // 灵敏度PWM通道 (PWM channel for sensitivity setting)

// 传感器参数范围 (Sensor Parameter Ranges)
// 根据传感器说明书P6，最小延时时间为2秒，最大为3600秒。
#define S18L262_MIN_ONTIME          2   // 最小延时时间(秒) (Minimum on-time in seconds)
#define S18L262_MAX_ONTIME          3600 // 最大延时时间(秒) (Maximum on-time in seconds)
// 根据传感器说明书P7，灵敏度SENS引脚电压0V对应最高灵敏度(100%)，VDD/2对应最低灵敏度(0%)。
#define S18L262_MIN_SENS            0   // 最小灵敏度(%) - 对应传感器SENS引脚电压VDD/2 (Minimum sensitivity (%))
#define S18L262_MAX_SENS            100 // 最大灵敏度(%) - 对应传感器SENS引脚电压0V (Maximum sensitivity (%))

// 中断类型 (Interrupt Types)
typedef enum {
    S18L262_INTR_NONE = 0,      // 无中断 (No interrupt)
    S18L262_INTR_RISING = 1,    // 上升沿中断(检测到人体) (Rising edge interrupt)
    S18L262_INTR_FALLING = 2,   // 下降沿中断(人体离开) (Falling edge interrupt)
    S18L262_INTR_ANY = 3        // 任意边沿中断 (Any edge interrupt)
} s18l262_intr_type_t;

// 传感器配置 (Sensor Configuration)
typedef struct {
    int pir_rel_pin;            // 人体感应输出引脚 (PIR REL output pin number, use -1 for default)
    int dac_set_ontime_pin;     // 延时时间设置引脚 (ONTIME setting pin number, use -1 for default)
    int dac_set_sens_pin;       // 灵敏度设置引脚 (SENS setting pin number, use -1 for default)
    bool use_pwm;               // 是否使用PWM模拟DAC (True to use PWM for ONTIME/SENS, false for GPIO high)
    s18l262_intr_type_t intr_type; // 中断类型 (Interrupt type for PIR_REL pin)
} s18l262_config_t;

// 中断回调函数类型 (Interrupt Handler Function Type)
typedef void (*s18l262_intr_handler_t)(void* arg);

/**
 * @brief 初始化S18L262传感器 (Initialize the S18L262 sensor)
 *
 * @param config 传感器配置 (Pointer to the sensor configuration structure)
 * @return esp_err_t ESP_OK: 成功 (Success), 其他: 失败 (Other ESP_ERR_ codes for failure)
 */
esp_err_t s18l262_init(const s18l262_config_t* config);

/**
 * @brief 设置中断处理函数 (Set the interrupt handler function for PIR events)
 *
 * @param handler 中断处理函数 (The handler function to call on interrupt)
 * @param arg 传递给中断处理函数的参数 (Argument to pass to the handler function)
 * @return esp_err_t ESP_OK: 成功 (Success), 其他: 失败 (Failure)
 */
esp_err_t s18l262_set_intr_handler(s18l262_intr_handler_t handler, void* arg);

/**
 * @brief 读取传感器状态 (Read the current state of the PIR_REL pin)
 *
 * @return bool true: 检测到人体 (Human detected - pin is high), false: 未检测到人体 (No human detected - pin is low)
 */
bool s18l262_read_state(void);

/**
 * @brief 设置延时时间 (Set the on-time delay)
 * @details 当 use_pwm 为 false 时，此函数在当前实现中仅记录日志，因为引脚已固定拉低。
 * PWM占空比与秒数的映射关系需要根据RC滤波器和传感器规格进行校准。
 * (When use_pwm is false, this function currently only logs as the pin is fixed high.
 * The mapping from seconds to PWM duty cycle requires calibration based on the RC filter and sensor specs.)
 *
 * @param seconds 延时时间(秒)，范围: S18L262_MIN_ONTIME ~ S18L262_MAX_ONTIME
 * (Delay time in seconds, range: S18L262_MIN_ONTIME to S18L262_MAX_ONTIME)
 * @return esp_err_t ESP_OK: 成功 (Success), 其他: 失败 (Failure)
 */
esp_err_t s18l262_set_ontime(uint16_t seconds);

/**
 * @brief 设置灵敏度 (Set the sensitivity)
 * @details 当 use_pwm 为 false 时，此函数在当前实现中仅记录日志，因为引脚已固定拉低。
 * PWM占空比与灵敏度百分比的映射关系需要根据RC滤波器和传感器规格进行校准。
 * (When use_pwm is false, this function currently only logs as the pin is fixed high.
 * The mapping from sensitivity percentage to PWM duty cycle requires calibration.)
 *
 * @param sensitivity_percent 灵敏度(%)，范围: S18L262_MIN_SENS ~ S18L262_MAX_SENS
 * (Sensitivity in percent, range: S18L262_MIN_SENS to S18L262_MAX_SENS)
 * @return esp_err_t ESP_OK: 成功 (Success), 其他: 失败 (Failure)
 */
esp_err_t s18l262_set_sensitivity(uint8_t sensitivity_percent);

/**
 * @brief 启用传感器功能 (主要指启用PIR_REL引脚中断，并允许PWM设置生效)
 * (Enable sensor functionality, primarily enables interrupts on PIR_REL and allows PWM settings to take effect)
 *
 * @return esp_err_t ESP_OK: 成功 (Success), 其他: 失败 (Failure)
 */
esp_err_t s18l262_enable(void);

/**
 * @brief 禁用传感器功能 (主要指禁用PIR_REL引脚中断)
 * (Disable sensor functionality, primarily disables interrupts on PIR_REL)
 *
 * @return esp_err_t ESP_OK: 成功 (Success), 其他: 失败 (Failure)
 */
esp_err_t s18l262_disable(void);

/**
 * @brief 反初始化S18L262传感器驱动 (De-initialize the S18L262 sensor driver)
 * @details 释放占用的资源，重置GPIO引脚。
 * (Releases allocated resources and resets GPIO pins.)
 *
 * @return esp_err_t ESP_OK: 成功 (Success), 其他: 失败 (Failure)
 */
esp_err_t s18l262_deinit(void);

#endif /* S18L262_H */
