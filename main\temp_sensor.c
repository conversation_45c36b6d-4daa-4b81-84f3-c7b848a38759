/**
 * @file temp_sensor.c
 * @brief ESP32-S3内部温度传感器驱动实现
 * @version 1.0
 * @date 2024-08-10
 */

#include <stdio.h>
#include "esp_log.h"
#include "esp_err.h"
#include "esp_idf_version.h"
#include "temp_sensor.h"

// ESP-IDF v4.4.x使用的是temp_sensor.h
#include "driver/temp_sensor.h"

static const char *TAG = "TEMP_SENSOR";

// 在ESP-IDF v4.4.8中，温度传感器API不使用句柄
// 而是直接使用全局函数

esp_err_t temp_sensor_init(void)
{
    // 配置温度传感器
    temp_sensor_config_t temp_sensor = TSENS_CONFIG_DEFAULT();

    // 安装温度传感器驱动
    esp_err_t ret = temp_sensor_set_config(temp_sensor);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "温度传感器配置失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 启用温度传感器
    ret = temp_sensor_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "温度传感器启动失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "温度传感器初始化成功");
    return ESP_OK;
}

esp_err_t temp_sensor_deinit(void)
{
    // 停止温度传感器
    esp_err_t ret = temp_sensor_stop();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "温度传感器停止失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "温度传感器反初始化成功");
    return ESP_OK;
}

esp_err_t read_chip_temperature(float *temperature)
{
    // 参数检查
    if (temperature == NULL) {
        ESP_LOGE(TAG, "无效的参数");
        return ESP_ERR_INVALID_ARG;
    }

    // 读取温度值 - 使用ESP-IDF v4.4.8的API
    esp_err_t ret = temp_sensor_read_celsius(temperature);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "读取温度失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // ESP_LOGI(TAG, "当前温度: %.2f °C", *temperature);
    return ESP_OK;
}
