/**
 * @file ble_config_service.h
 * @brief BLE Configuration Service
 * @version 1.0
 * @date 2024-08-15
 */

#ifndef BLE_CONFIG_SERVICE_H
#define BLE_CONFIG_SERVICE_H

#include <stdbool.h>
#include "esp_err.h"
#include "host/ble_hs.h"

#ifdef __cplusplus
extern "C" {
#endif

// Configuration mode definitions
typedef enum {
    CONFIG_MODE_RAW_DATA = 0x01,           // 原始数据采集模式
    CONFIG_MODE_VITALS_MONITOR = 0x02,     // 生命体征监测模式
    CONFIG_MODE_FALL_DETECTION = 0x03,     // 跌倒检测功能模式
    CONFIG_MODE_PRESENCE_DETECTION = 0x04, // 人体存在检测模式
} ble_config_mode_t;

// Command header (changed from 0xA5 to avoid conflict with OTA service)
#define CONFIG_CMD_HEADER 0xB5

// Response codes
#define CONFIG_RESPONSE_SUCCESS 0x01
#define CONFIG_RESPONSE_FAILURE 0x00

/**
 * @brief Initialize the BLE Configuration Service
 *
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_config_service_init(void);

/**
 * @brief Register callback for GATT service registration
 *
 * @param ctxt GATT registration context
 * @param arg User argument
 */
void ble_config_register_cb(struct ble_gatt_register_ctxt *ctxt, void *arg);

/**
 * @brief Get the current configuration mode
 *
 * @return ble_config_mode_t Current configuration mode
 */
ble_config_mode_t ble_config_get_current_mode(void);

/**
 * @brief Send simulated data based on the current mode
 *
 * This function should be called periodically to send simulated data
 * based on the current configuration mode.
 *
 * @return esp_err_t ESP_OK on success, error code otherwise
 */
esp_err_t ble_config_send_simulated_data(void);

#ifdef __cplusplus
}
#endif

#endif /* BLE_CONFIG_SERVICE_H */
