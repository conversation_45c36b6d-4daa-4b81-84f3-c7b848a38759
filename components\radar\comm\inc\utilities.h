/**
  ******************************************************************************
  * @file    utilities.h
  * <AUTHOR>
  * @brief   utilities header file
  ******************************************************************************
  */
#ifndef __UTILITIES_H__
#define __UTILITIES_H__

#ifdef __cplusplus
 extern "C" {
#endif

#include <stdint.h>


#define __ALIGN(n)      __attribute__((aligned (n)))
#define __SRAM4_DATA    __attribute__((section(".Sram4Data")))

#define ARRAY_SIZE(X)    (sizeof(X)/sizeof(X[0]))

void RunFailed(uint8_t *file, uint32_t line);

#ifdef __cplusplus
}
#endif

#endif

