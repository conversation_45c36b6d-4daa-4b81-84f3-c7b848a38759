/*
 * bsp_esp32_spi.h
 *
 *  Created on: 2023年9月5日
 *      Author: KB402
 */

#ifndef PLATFORM_INC_BSP_ESP32_SPI_H_
#define PLATFORM_INC_BSP_ESP32_SPI_H_

#ifdef __cplusplus
 extern "C" {
#endif


#include "global_conf.h"
#include "../../comm/inc/utilities.h"
#include "driver/spi_slave.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "freertos/timers.h"
#include "freertos/event_groups.h"

 // 是否启用事件组（0表示禁用，1表示启用）
 #define USE_EVENT_GROUP     (0)

 // 数据处理标志位，用于事件组中的数据处理任务
 #define DATA_PROCESS_BIT    (1 << 0)




typedef enum
{
    DMA_RECV_FLAG_MEM_0        = 0,
    DMA_RECV_FLAG_MEM_1        = 1,  //XXX改为1缓存
    DMA_RECV_FLAG_MAX
}dmaRecvFlagEnum;



#define SPI_FRAME_DLEN_MAX      RADAR_DATA_MAX_LEN
#define SPI_FRAME_HLEN          (4)
#define SPI_FRAME_TLEN          (4)

#define SPI_FRAME_WRAPPER           (12)
#define SPI_FRAME_PADDING       (24)
#define SPI_FRAME_WRAPPER_HEAD (0x484C4349)
#define SPI_FRAME_WRAPPER_TAIL (0x544C4349)
#define SPI_FRAME_WRAPPER_SHIFT 8
#define SPI_FRAME_LEN_MAX       (SPI_FRAME_WRAPPER + SPI_FRAME_DLEN_MAX + SPI_FRAME_HLEN + SPI_FRAME_TLEN + SPI_FRAME_PADDING)

#define DATA_RECV_BUF_SIZE           (SPI_FRAME_LEN_MAX * 2) /*ping-pong buffer*/

extern uint8_t g_dataRecvBuf[CHANNEL_MAX][DATA_RECV_BUF_SIZE];
extern volatile uint8_t g_dataRecvFlag[CHANNEL_MAX][DMA_RECV_FLAG_MAX];


extern QueueHandle_t SpiDataQueue;

/**
 * @brief 雷达数据结构体
 */
typedef struct {
    uint8_t *buf;      // 数据缓冲区指针
    uint16_t len;      // 数据长度
    uint8_t dmaFlag;   // DMA缓冲区标志
} RADAR_DATA_T;

/**
 * @brief 数据接收回调函数类型
 * @param channel 通道号
 * @param data 雷达数据指针
 */
typedef void (*spi_data_recv_callback_t)(void);

/**
 * @brief 注册SPI数据接收回调函数
 * @param callback 回调函数指针
 */
void bsp_spi_register_recv_callback(spi_data_recv_callback_t callback);

/**
 * @brief 初始化SPI从机
 */
void bsp_spi_slave_init(uint16_t dmaBufLen, bool useMcuPackWrapper);

/**
 * @brief 反初始化SPI从机
 */
void bsp_spi_deinit(void);

#ifdef __cplusplus
}
#endif


#endif /* PLATFORM_INC_BSP_ESP32_SPI_H_ */
