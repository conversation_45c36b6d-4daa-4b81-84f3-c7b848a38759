/*
 * bsp_esp32_uart.c
 *
 * ESP32 UART驱动实现，支持回调函数机制
 *
 * Created on: 2023年9月5日
 * Updated on: 2024年5月21日
 */

#include <string.h>
#include "bsp_esp32_uart.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

// UART配置
#define EX_UART_NUM UART_NUM_0
static const int RX_BUF_SIZE = 1024;
static const char *TAG = "bsp_esp32_uart.c";

// 全局变量
CMD_RECV_T g_cmdRecv;
static QueueHandle_t uart0_queue;

// 回调函数指针
static uart_cmd_recv_callback_t g_uart_cmd_recv_callback = NULL;

// 函数声明
static void uart_data_event_processor(int uart_num, uart_event_t event);
static void uart_event_task(void *pvParameters);

/**
 * @brief 注册UART命令接收回调函数
 * @param callback 回调函数指针
 */
void bsp_uart_register_cmd_callback(uart_cmd_recv_callback_t callback)
{
    g_uart_cmd_recv_callback = callback;
}

/**
 * @brief UART事件处理任务
 * @param pvParameters 任务参数
 */
static void uart_event_task(void *pvParameters)
{
    uart_event_t event;
    size_t buffered_size;
    uint8_t* dtmp = (uint8_t*) malloc(RX_BUF_SIZE);

    if (dtmp == NULL) {
        ESP_LOGE(TAG, "Failed to allocate memory for UART event task");
        vTaskDelete(NULL);
        return;
    }

    for(;;) {
        // 等待UART事件
        if(xQueueReceive(uart0_queue, (void *)&event, portMAX_DELAY)) {
            bzero(dtmp, RX_BUF_SIZE);

            switch(event.type) {
                // 接收数据事件
                case UART_DATA:
                    uart_data_event_processor(EX_UART_NUM, event);
                    break;

                // 硬件FIFO溢出事件
                case UART_FIFO_OVF:
                    ESP_LOGI(TAG, "Hardware FIFO overflow");
                    uart_flush_input(EX_UART_NUM);
                    xQueueReset(uart0_queue);
                    break;

                // UART环形缓冲区满事件
                case UART_BUFFER_FULL:
                    ESP_LOGI(TAG, "Ring buffer full");
                    uart_flush_input(EX_UART_NUM);
                    xQueueReset(uart0_queue);
                    break;

                // UART接收中断事件
                case UART_BREAK:
                    ESP_LOGI(TAG, "UART RX break");
                    break;

                // UART奇偶校验错误事件
                case UART_PARITY_ERR:
                    ESP_LOGI(TAG, "UART parity error");
                    break;

                // UART帧错误事件
                case UART_FRAME_ERR:
                    ESP_LOGI(TAG, "UART frame error");
                    break;

                // UART模式检测事件
                case UART_PATTERN_DET:
                    uart_get_buffered_data_len(EX_UART_NUM, &buffered_size);
                    int pos = uart_pattern_pop_pos(EX_UART_NUM);
                    ESP_LOGI(TAG, "[UART PATTERN DETECTED] pos: %d, buffered size: %d", pos, buffered_size);
                    break;

                // 其他事件
                default:
                    ESP_LOGI(TAG, "UART event type: %d", event.type);
                    break;
            }
        }

    }

    // 释放资源（实际上这里永远不会执行到，因为任务是一个无限循环）
    free(dtmp);
    vTaskDelete(NULL);
}

/**
 * @brief UART接收中断处理函数
 * @param uart_num UART端口号
 */
static void uart_data_event_processor(int uart_num, uart_event_t event)
{
    int cnt;
    size_t bytes_to_read = event.size;

    if (bytes_to_read == 0) {
        // 通常 UART_DATA 事件的 event.size > 0，但也可能需要处理这种理论上的情况
        return;
    }

    // 如果事件报告的大小超过了我们单个应用缓冲区的大小，则进行截断
    // (这种情况暗示UART驱动的RX_BUF_SIZE可能大于APP_RX_DATA_SIZE，或者有其他配置问题)
    if (bytes_to_read > APP_RX_DATA_SIZE) {
        ESP_LOGW("YOUR_TAG", "event.size (%u) is greater than APP_RX_DATA_SIZE (%d). Reading only APP_RX_DATA_SIZE.", bytes_to_read, APP_RX_DATA_SIZE);
        bytes_to_read = APP_RX_DATA_SIZE;
    }
    // 读取UART接收到的数据
    cnt = uart_read_bytes(uart_num, &g_cmdRecv.buf[g_cmdRecv.bufRecv],
                         APP_RX_DATA_SIZE, 20 / portTICK_PERIOD_MS);  // 20ms超时

    if (cnt <= 0) {
        return;
    }

    g_cmdRecv.cmdReady = 1;
    g_cmdRecv.bufProc = g_cmdRecv.bufRecv;
    g_cmdRecv.bufRecv = (++g_cmdRecv.bufRecv) % CMD_RECV_BUF_MAX;
    g_cmdRecv.bufLen = cnt;

    // 如果注册了回调函数，调用回调函数处理命令
    if (g_uart_cmd_recv_callback != NULL) {
        CMD_DATA_T cmdData;
        cmdData.buf = g_cmdRecv.buf[g_cmdRecv.bufProc];
        cmdData.len = g_cmdRecv.bufLen;
        g_uart_cmd_recv_callback(&cmdData);
    }

}



// log口
void bsp_uart0_init(uint32_t baud_rate) {

    const uart_config_t uart_config = {
        .baud_rate = baud_rate,                    //设置波特率    115200
        .data_bits = UART_DATA_8_BITS,          //设置数据位    8位
        .parity = UART_PARITY_DISABLE,          //设置奇偶校验  不校验
        .stop_bits = UART_STOP_BITS_1,          //设置停止位    1
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,  //设置硬件流控制 不使能
        .source_clk = UART_SCLK_APB,            //设置时钟源
    };
    // 安装串口驱动 串口编号、接收buff、发送buff、事件队列、分配中断的标志
    // 只要你用 uart_driver_install 并设置合适的缓冲区，底层就会自动用DMA（ESP32S3硬件支持）。你无需手动“打开DMA”
    uart_driver_install(UART_NUM_0, RX_BUF_SIZE * 2, RX_BUF_SIZE * 2, 20, &uart0_queue, 0);
    //串口参数配置 串口号、串口配置参数
    uart_param_config(UART_NUM_0, &uart_config);
    //设置串口引脚号 串口编号、tx引脚、rx引脚、rts引脚、cts引脚 对于后四个参数，可以提供一个宏UART_PIN_NO_CHANGE来保留已经分配的引脚
    uart_set_pin(UART_NUM_0, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE);

    //Create a task to handler UART event from ISR
	xTaskCreate(uart_event_task, "uart_event_task", 2048, NULL, 12, NULL);
}



